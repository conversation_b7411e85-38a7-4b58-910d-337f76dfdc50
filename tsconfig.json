{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@lib/*": ["./src/lib/*"], "@config/*": ["./src/config/*"], "@hooks/*": ["./src/hooks/*"], "@types/*": ["./src/types/*"], "@utils/*": ["./src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "src/app/test-api-routes/**/*", "src/app/test-data-layer/**/*", "src/app/test-featured-debug/**/*", "src/app/api/test-email/**/*", "__tests__/**/*", "tests/**/*", "docs/**/*", "src/__tests__/**/*", "src/components/**/__tests__/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}