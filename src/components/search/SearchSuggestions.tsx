'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useDebounce } from '@/hooks/useDebounce';
import { logger } from '@/lib/utils/logger';

interface Suggestion {
  name: string;
  slug: string;
  id: string;
  type: 'category' | 'brand' | 'product';
}

interface SearchSuggestionsProps {
  query: string;
  onSelect?: (suggestion: string) => void; // Keep for backward compatibility
  onClose?: () => void; // New: Close suggestions dropdown
  onSubmitSearch?: (query: string) => void; // New: Submit form directly
  onSearchSubmit?: () => void; // New: Notify parent of search submission
}

export function SearchSuggestions({ query, onSelect, onClose, onSubmitSearch, onSearchSubmit }: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const suggestionsRef = useRef<HTMLUListElement>(null);
  const debouncedQuery = useDebounce(query, 200);
  const router = useRouter();

  // Fetch suggestions (public endpoint)
  useEffect(() => {
    if (debouncedQuery.length > 2) {
      setIsLoading(true);
      fetch(`/api/search/suggestions?q=${encodeURIComponent(debouncedQuery)}`)
        .then(res => res.json())
        .then(data => {
          const formattedSuggestions = [
            ...data.categories.map((c: any) => ({ ...c, type: 'category' as const })),
            ...data.brands.map((b: any) => ({ ...b, type: 'brand' as const })),
            ...data.products.map((p: any) => ({ ...p, type: 'product' as const }))
          ];
          setSuggestions(formattedSuggestions.slice(0, 6));
          setSelectedIndex(-1); // Reset selection when suggestions change
        })
        .catch(error => {
          console.error('Error fetching suggestions:', error);
          setSuggestions([]);
        })
        .finally(() => setIsLoading(false));
    } else {
      setSuggestions([]);
      setSelectedIndex(-1);
    }
  }, [debouncedQuery]);

  // Auto-hide suggestions when no results after 500ms
  useEffect(() => {
    if (suggestions.length === 0 && debouncedQuery.length > 2 && !isLoading) {
      const timeout = setTimeout(() => {
        onClose?.();
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [suggestions, debouncedQuery, isLoading, onClose]);

  // Type-aware suggestion selection handler
  const handleSuggestionSelect = useCallback((suggestion: Suggestion) => {
    // Analytics tracking
    logger.info('Search suggestion selected', {
      query: debouncedQuery,
      suggestionType: suggestion.type,
      suggestionId: suggestion.id,
      suggestionName: suggestion.name
    });

    // Type-aware routing - clicking suggestion = typing the full text
    let destinationUrl = '';
    switch (suggestion.type) {
      case 'brand':
        destinationUrl = `/search?brand=${encodeURIComponent(suggestion.slug)}`;
        break;
      case 'category':
        destinationUrl = `/search?category=${encodeURIComponent(suggestion.slug)}`;
        break;
      case 'product':
        destinationUrl = `/products/${suggestion.slug}`;
        // Store product name for search input persistence
        try {
          sessionStorage.setItem('lastClickedProduct', suggestion.name);
        } catch (error) {
          // SessionStorage not available - ignore
        }
        break;
      default:
        // Fallback to generic search (query-phrase suggestions)
        destinationUrl = `/search?q=${encodeURIComponent(suggestion.name)}`;
    }

    // Analytics tracking with destination
    logger.info('Search suggestion navigation', {
      query: debouncedQuery,
      suggestionType: suggestion.type,
      suggestionId: suggestion.id,
      destinationUrl
    });

    // Navigate to destination
    router.push(destinationUrl);
    
    // Close the suggestions dropdown after selection
    onClose?.();
    
    // Notify parent of search submission (for mobile menu closure)
    onSearchSubmit?.();
    
    // Don't call onSelect for type-aware routing to avoid conflicts
    // onSelect?.(suggestion.name); // Commented out to prevent SearchBar from overriding our navigation
  }, [debouncedQuery, router, onClose, onSearchSubmit]);

  // Handle keyboard navigation only when suggestions are visible
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard events when suggestions are visible
      if (suggestions.length === 0 && debouncedQuery.length <= 2) return;
      
      // Handle Enter key even when no suggestions
      if (e.key === 'Enter') {
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          e.preventDefault();
          const selectedSuggestion = suggestions[selectedIndex];
          handleSuggestionSelect(selectedSuggestion);
        } else if (suggestions.length === 0 && debouncedQuery.length > 0) {
          // No suggestions - submit form directly
          e.preventDefault();
          onSubmitSearch?.(debouncedQuery);
        }
        return;
      }

      // Handle Escape key
      if (e.key === 'Escape') {
        onClose?.();
        return;
      }

      // For other keys, need suggestions to be present
      if (!suggestions.length) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        // Enter and Escape are handled above
      }
    };

    // Only add event listener when suggestions are visible or we're showing "no results"
    if (suggestions.length > 0 || (suggestions.length === 0 && debouncedQuery.length > 2)) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [suggestions, selectedIndex, onSelect, query, debouncedQuery, onSubmitSearch, onClose, handleSuggestionSelect]);

  // Scroll selected suggestion into view
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionsRef.current) {
      const selectedItem = suggestionsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedItem) {
        selectedItem.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  if (!query || query.length < 3) return null;

  const handleSuggestionClick = (e: React.MouseEvent, suggestion: Suggestion) => {
    e.preventDefault();
    handleSuggestionSelect(suggestion);
  };

  return (
    <div className="absolute top-full mt-1 w-full bg-white shadow-lg rounded-lg overflow-hidden z-50">
      {isLoading ? (
        <div className="p-4 text-gray-500 text-sm">Loading suggestions...</div>
      ) : suggestions.length > 0 ? (
        <ul ref={suggestionsRef}>
          {suggestions.map((suggestion, index) => (
            <li 
              key={`${suggestion.type}-${suggestion.name}-${index}`}
              className={`px-4 py-2 cursor-pointer transition-colors ${
                index === selectedIndex 
                  ? 'bg-primary/10 text-primary' 
                  : 'hover:bg-gray-100 text-gray-700'
              }`}
              onClick={(e) => handleSuggestionClick(e, suggestion)}
              onMouseEnter={() => setSelectedIndex(index)}
              onMouseDown={(e) => e.preventDefault()} // Prevent input blur on click
              role="option"
              aria-selected={index === selectedIndex}
            >
              <div className="flex items-center">
                <span className="capitalize text-xs text-gray-500 w-20 flex-shrink-0">
                  {suggestion.type}:
                </span>
                <span className="truncate">{suggestion.name}</span>
              </div>
            </li>
          ))}
        </ul>
      ) : debouncedQuery.length > 2 ? (
        <div className="p-4 text-center text-gray-500 text-sm">
          <div>No suggestions found for &quot;{debouncedQuery}&quot;</div>
          <div className="text-xs mt-1 text-gray-400">Press Enter to search</div>
        </div>
      ) : null}
    </div>
  );
}
