// CI Test Setup
// Configures the test environment specifically for GitHub Actions

import '@testing-library/jest-dom'

// Import mocks
import '../../__mocks__/supabase'
import '../../__mocks__/auth'

// Mock console methods to reduce noise in CI
const originalError = console.error
const originalWarn = console.warn

beforeAll(() => {
  // Mock console.error to ignore known warnings in CI
  console.error = (...args: any[]) => {
    const message = args[0]
    
    // Ignore specific CI-related warnings
    if (
      typeof message === 'string' && (
        message.includes('Warning: ReactDOM.render is deprecated') ||
        message.includes('Warning: componentWillReceiveProps') ||
        message.includes('punycode') ||
        message.includes('Invalid CIDR') ||
        message.includes('FATAL: Invalid CIDR range')
      )
    ) {
      return
    }
    
    originalError(...args)
  }
  
  // Mock console.warn to ignore development warnings
  console.warn = (...args: any[]) => {
    const message = args[0]
    
    if (
      typeof message === 'string' && (
        message.includes('React Hook') ||
        message.includes('exhaustive-deps') ||
        message.includes('MODULE_TYPELESS_PACKAGE_JSON')
      )
    ) {
      return
    }
    
    originalWarn(...args)
  }
})

afterAll(() => {
  // Restore original console methods
  console.error = originalError
  console.warn = originalWarn
})

// Mock window.fetch for API tests
global.fetch = jest.fn()

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  unobserve: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  unobserve: jest.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock environment variables for CI
process.env.NODE_ENV = 'test'
process.env.CI = 'true'
process.env.NEXT_PUBLIC_SITE_URL = 'http://localhost:3000'