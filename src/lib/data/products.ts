import type { SupabaseClient } from '@supabase/supabase-js';
import type { Brand, FilterPromotion, ProductResponse, ProductFilters, TransformedProduct, PaginatedProductsResponse } from './types';
import { logger } from '../utils/logger';
import { TIMEOUT_CONFIG } from '../timeoutConfig';
import { cachedSearchProducts } from '../cache/searchCache';
import { monitorQuery, optimizeSearchQuery } from '../optimization/queryOptimizer';

export function transformProduct(product: any): TransformedProduct {
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description || '',
    images: product.images || [],
    specifications: product.specifications || null,
    status: product.status || 'active',
    isFeatured: product.is_featured || false,
    isSponsored: product.is_sponsored || false,
    cashbackAmount: product.cashback_amount || 0,
    minPrice: null, // This should be calculated separately if needed
    modelNumber: product.model_number || '',
    createdAt: product.created_at,
    updatedAt: product.updated_at,
    brand: product.brand ? {
      id: product.brand.id,
      name: product.brand.name,
      slug: product.brand.slug,
      logoUrl: product.brand.logo_url || null,
      description: product.brand.description || null,
    } : null,
    category: product.category || null,
    promotion: product.promotion ? {
      id: product.promotion.id,
      title: product.promotion.title,
      description: product.promotion.description || null,
      maxCashbackAmount: product.promotion.max_cashback_amount || 0,
      purchaseStartDate: product.promotion.purchase_start_date,
      purchaseEndDate: product.promotion.purchase_end_date,
      claimStartOffsetDays: product.promotion.claim_start_offset_days,
      claimWindowDays: product.promotion.claim_window_days,
      termsUrl: product.promotion.terms_url ?? null,
      termsDescription: product.promotion.terms_description ?? null,
      status: product.promotion.status || 'active',
      isFeatured: product.promotion.is_featured || false,
    } : null,
    retailerOffers: product.retailer_offers || [],
  };
}

export async function getProductPageData(supabase: SupabaseClient, idOrSlug: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(idOrSlug);

  if (isUUID) {
    return await getProductWithSimilar(supabase, idOrSlug);
  } else {
    const product = await getProductBySlug(supabase, idOrSlug);
    if (!product) return null;
    
    const similarProducts = await getSimilarProducts(supabase, product.category?.id || '', product.id).catch(() => []);
    
    return { product, similarProducts };
  }
}

export async function getProducts(supabase: SupabaseClient, filters: ProductFilters = {}): Promise<PaginatedProductsResponse> {
  let query = supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `, { count: 'exact' });

  if (filters.brandId) {
    query = query.eq('brand_id', filters.brandId);
  }
  if (filters.categoryId) {
    query = query.eq('category_id', filters.categoryId);
  }
  if (filters.promotionId) {
    query = query.eq('promotion_id', filters.promotionId);
  }
  if (filters.status) {
    query = query.eq('status', filters.status);
  }
  
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 20;
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw error;
  }

  const transformedProducts = (data || []).map(transformProduct);

  return {
    product: transformedProducts,
    similarProducts: [],
    pagination: {
      page,
      pageSize,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / pageSize),
      hasNext: page < Math.ceil((count || 0) / pageSize),
      hasPrev: page > 1
    }
  };
}

export async function getProduct(supabase: SupabaseClient, id: string): Promise<TransformedProduct | null> {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching product by ID:', error);
    return null;
  }

  if (!data) return null;

  return transformProduct(data);
}

export async function getProductBySlug(supabase: SupabaseClient, slug: string): Promise<TransformedProduct | null> {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('slug', slug)
    .single();

  if (error) {
    console.error('Error fetching product by slug:', error);
    return null;
  }

  if (!data) return null;

  return transformProduct(data);
}

export async function getProductWithSimilar(supabase: SupabaseClient, id: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const product = await getProduct(supabase, id);
  if (!product) return null;

  const similarProducts = await getSimilarProducts(supabase, product.category?.id || '', product.id).catch(() => []);

  return { product, similarProducts };
}

export async function getSimilarProducts(supabase: SupabaseClient, categoryId: string, productId: string): Promise<TransformedProduct[]> {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      promotion:promotion_id (*),
      retailer_offers:product_retailer_offers(*)
    `)
    .eq('category_id', categoryId)
    .neq('id', productId)
    .limit(10);

  if (error) {
    console.error('Error fetching similar products:', error);
    return [];
  }

  return (data || []).map(transformProduct);
}

export async function getFeaturedProducts(supabase: SupabaseClient, limit: number = 10): Promise<TransformedProduct[]> {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('is_featured', true)
    .limit(limit);

  if (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }

  if (!data) return [];

  return data.map(transformProduct);
}

export async function getFilterOptions(supabase: SupabaseClient): Promise<{ brands: Brand[]; promotions: FilterPromotion[]; }> {
  try {
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('*')
      .order('name', { ascending: true });

    console.log('Supabase brands response - data:', brands);
    console.log('Supabase brands response - error:', brandsError);

    if (brandsError || !brands) {
      console.error('Error fetching brands or no data returned:', brandsError || 'No data');
      return { brands: [], promotions: [] };
    }

    const { data: promotions, error: promotionsError } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brands!inner(*)
      `)
      .eq('status', 'active')
      .order('title', { ascending: true });

    if (promotionsError) {
      console.error('Error fetching promotions:', promotionsError);
      return { brands: [], promotions: [] };
    }

    const transformedPromotions = promotions.map((promo: any) => ({
      ...promo,
      brand_name: promo.brand?.name || 'Unknown Brand',
    }));

    return {
      brands: brands || [],
      promotions: transformedPromotions || [],
    };
  } catch (error) {
    console.error('Unexpected error in getFilterOptions:', error);
    return { brands: [], promotions: [] };
  }
}

// Internal search function (without caching)
async function _searchProductsInternal(supabase: SupabaseClient, query: string, page: number = 1, pageSize: number = 20): Promise<{ products: TransformedProduct[], totalCount: number }> {
  // Optimize the search query
  const { optimizedQuery, suggestions } = optimizeSearchQuery(query);

  // Log optimization suggestions in development
  if (process.env.NODE_ENV === 'development' && suggestions.length > 0) {
    logger.info('Search query optimization suggestions', {
      originalQuery: query,
      optimizedQuery,
      suggestions
    });
  }

  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Monitor the database query performance
  const result = await monitorQuery(
    `search-products-${optimizedQuery.substring(0, 20)}`,
    async () => {
      return await supabase
        .from('products')
        .select(`
          *,
          brand:brand_id (id, name, slug, logo_url, description),
          promotion:promotion_id (*)
        `, { count: 'exact' })
        .ilike('name', `%${optimizedQuery}%`)
        .range(from, to);
    },
    {
      timeout: TIMEOUT_CONFIG.DATABASE.QUERY,
      logSlowQueries: true,
      slowQueryThreshold: 2000
    }
  );

  const { data, error, count } = result;

  if (error) {
    logger.error('Error searching products', error, {
      query: optimizedQuery,
      originalQuery: query,
      page,
      pageSize,
      from,
      to
    });
    return { products: [], totalCount: 0 };
  }

  const products = (data || []).map(transformProduct);
  return { products, totalCount: count || 0 };
}

// Public search function with caching
export async function searchProducts(supabase: SupabaseClient, query: string, page: number = 1, pageSize: number = 20): Promise<{ products: TransformedProduct[], totalCount: number }> {
  // Use cached search for better performance
  // The `supabase` client is passed to the internal search function.
  return cachedSearchProducts(
    (q, p, ps) => _searchProductsInternal(supabase, q, p, ps),
    query,
    page,
    pageSize
  );
}


