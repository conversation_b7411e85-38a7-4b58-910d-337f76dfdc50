//src/app/layout.tsx
// This is the layout component that wraps the entire application.
// It includes the header, footer, and global styles.
// It also provides the default SEO metadata for the entire application.

// SECURITY: Import environment guard-rail first to validate security settings
import '@/lib/env-guard'

import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Toaster } from 'react-hot-toast'
import { constructMetadata } from '@/lib/metadata-utils'
import { WebVitals } from '@/components/performance/WebVitals'
import { PerformanceDashboardWrapper } from '@/components/debug/PerformanceDashboardWrapper'
import { Suspense } from 'react'
const inter = Inter({ subsets: ['latin'] })

// Export metadata using our utility function
export const metadata: Metadata = constructMetadata({
  // No need to specify title or description here as our utility
  // will use the defaults from siteConfig
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} min-h-screen flex flex-col`}>
        <Providers>
          <Suspense fallback={<div className="h-16" />}>
            <Header />
          </Suspense>
          <main className="flex-1">
            {children}
          </main>
          <Footer />
          <Toaster
            position="bottom-right"
            toastOptions={{
              className: 'bg-white text-primary border border-primary/10',
              duration: 3000,
            }}
          />
          {/* Core Web Vitals monitoring for performance optimization */}
          <WebVitals
            debug={process.env.NODE_ENV === 'development'}
            reportToAnalytics={process.env.NODE_ENV === 'production'}
          />
          {/* Performance Dashboard for development */}
          <PerformanceDashboardWrapper />
        </Providers>
      </body>
    </html>
  )
}