// src/app/retailers/page.tsx - Retailers listing page with Server-Side Rendering
// Displays all 1,658+ retailers with SEO optimization and search functionality

import { Suspense } from 'react';
import { constructMetadata } from '@/lib/metadata-utils';
import { getRetailers } from '@/lib/data/retailers';
import { RetailersPageClient } from '@/components/pages/RetailersPageClient';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

interface RetailersPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    featured?: string;
  }>;
}

// Generate metadata for SEO optimization
export async function generateMetadata() {
  return constructMetadata({
    title: 'All Retailers - Cashback Deals & Offers',
    description: 'Browse over 1,600 retailers offering cashback deals and exclusive offers. Find your favorite stores and start earning money back on your purchases.',
    pathname: '/retailers'
  });
}

// Loading skeleton component for better UX during data fetching
function RetailersPageSkeleton() {
  return (
    <div className="container py-12">
      {/* Header skeleton */}
      <div className="text-center mb-12">
        <div className="h-10 bg-gray-300 rounded w-64 mx-auto mb-4 animate-pulse"></div>
        <div className="h-6 bg-gray-300 rounded w-96 mx-auto animate-pulse"></div>
      </div>

      {/* Search and filters skeleton */}
      <div className="mb-8 space-y-4">
        <div className="h-12 bg-gray-300 rounded animate-pulse"></div>
        <div className="flex gap-4">
          <div className="h-10 bg-gray-300 rounded w-32 animate-pulse"></div>
          <div className="h-10 bg-gray-300 rounded w-32 animate-pulse"></div>
        </div>
      </div>

      {/* Stats skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="h-24 bg-gray-300 rounded animate-pulse"></div>
        ))}
      </div>

      {/* Retailers grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="h-48 bg-gray-300 rounded animate-pulse"></div>
        ))}
      </div>

      {/* Pagination skeleton */}
      <div className="flex justify-center mt-12">
        <div className="flex gap-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-10 w-10 bg-gray-300 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Main retailers page component - Server Component for optimal SEO
export default async function RetailersPage({ searchParams }: RetailersPageProps) {
  try {
    const params = await searchParams;
    // Parse search parameters
    const page = parseInt(params.page || '1', 10);
    const search = params.search || '';
    const featuredOnly = params.featured === 'true';

    // Server-side data fetching for improved SEO and Core Web Vitals
    const filters = {
      ...(search && { search }),
      ...(featuredOnly && { featured: true }),
    };

    const supabase = createServerSupabaseReadOnlyClient();
    const retailersData = await getRetailers(supabase, filters, page, 24); // 24 retailers per page

    return (
      <>
        {/* Suspense boundary for progressive loading */}
        <Suspense fallback={<RetailersPageSkeleton />}>
          <RetailersPageClient
            retailers={retailersData.data}
            //totalCount={retailersData.total}
            currentPage={page}
            searchQuery={search}
            featuredOnly={featuredOnly}
          />
        </Suspense>
      </>
    );
  } catch (error) {
    console.error('Error loading retailers page:', error);

    // Fallback content in case of data fetching errors
    return (
      <div className="container py-20 text-center">
        <h1 className="text-4xl font-bold text-primary mb-6">
          All Retailers
        </h1>
        <p className="text-lg text-foreground/70 mb-8">
          Browse over 1,600 retailers offering cashback deals and exclusive offers.
        </p>
        <p className="text-foreground/60">
          We&apos;re currently loading the latest retailers. Please refresh the page or try again later.
        </p>
      </div>
    );
  }
}
