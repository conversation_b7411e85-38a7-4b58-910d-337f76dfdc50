#!/usr/bin/env node

/**
 * Complete setup script for category hierarchy feature
 * Run with: npm run setup:hierarchy
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up category hierarchy feature...\n');

try {
  // Step 1: Check environment
  console.log('1. Checking environment variables...');
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Missing required environment variables:');
    console.error('   - NEXT_PUBLIC_SUPABASE_URL');
    console.error('   - SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }
  console.log('✅ Environment variables found\n');

  // Step 2: Run migration
  console.log('2. Running database migration...');
  execSync('node scripts/migrate-category-hierarchy.js', { stdio: 'inherit' });
  console.log('✅ Migration completed\n');

  // Step 3: Test functionality
  console.log('3. Testing category hierarchy...');
  execSync('node scripts/test-category-hierarchy.js', { stdio: 'inherit' });
  console.log('✅ Tests passed\n');

  // Step 4: Build application
  console.log('4. Building application...');
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build successful\n');

  console.log('🎉 Category hierarchy setup completed successfully!');
  console.log('\nNext steps:');
  console.log('- Run "npm run dev" to start development server');
  console.log('- Test search functionality with parent categories');
  console.log('- Monitor performance in production');

} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}