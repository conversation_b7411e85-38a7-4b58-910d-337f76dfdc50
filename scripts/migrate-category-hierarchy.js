#!/usr/bin/env node

/**
 * Migration script to set up category hierarchy function
 * Run with: node scripts/migrate-category-hierarchy.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Read and execute the migration SQL
    const migrationPath = path.join(__dirname, '../supabase/migrations/20250118_category_hierarchy_function.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('Migration file not found:', migrationPath);
      process.exit(1);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running category hierarchy migration...');
    
    // Execute the SQL directly
    const { error } = await supabase.rpc('exec', {
      sql: migrationSQL
    });

    if (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }

    console.log('✅ Category hierarchy migration completed successfully');
    
    // Test the function
    console.log('Testing category hierarchy function...');
    const { data, error: testError } = await supabase.rpc('get_category_hierarchy', {
      category_slug: 'home-garden'
    });

    if (testError) {
      console.error('Function test failed:', testError);
    } else {
      console.log(`✅ Function test passed. Found ${data?.length || 0} categories in hierarchy`);
    }

  } catch (error) {
    console.error('Migration script error:', error);
    process.exit(1);
  }
}

runMigration();