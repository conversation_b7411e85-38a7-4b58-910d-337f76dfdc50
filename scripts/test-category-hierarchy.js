#!/usr/bin/env node

/**
 * Test script for category hierarchy functionality
 * Run with: node scripts/test-category-hierarchy.js
 */

const { createClient } = require('@supabase/supabase-js');

async function testCategoryHierarchy() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  console.log('🧪 Testing category hierarchy functionality...\n');

  try {
    // Test 1: Check if function exists
    console.log('1. Testing function existence...');
    const { data: functions } = await supabase.rpc('get_category_hierarchy', {
      category_slug: 'test'
    });
    console.log('✅ Function exists and callable\n');

    // Test 2: Test with a known parent category
    console.log('2. Testing parent category (home-garden)...');
    const { data: parentTest, error: parentError } = await supabase.rpc('get_category_hierarchy', {
      category_slug: 'home-garden'
    });

    if (parentError) {
      console.error('❌ Parent category test failed:', parentError);
    } else {
      console.log(`✅ Found ${parentTest?.length || 0} categories in hierarchy`);
      if (parentTest && parentTest.length > 0) {
        console.log(`   - Is parent category: ${parentTest[0].is_parent}`);
      }
    }
    console.log('');

    // Test 3: Test with a child category
    console.log('3. Testing child category...');
    const { data: categories } = await supabase
      .from('categories')
      .select('slug')
      .not('parent_id', 'is', null)
      .limit(1);

    if (categories && categories.length > 0) {
      const childSlug = categories[0].slug;
      const { data: childTest, error: childError } = await supabase.rpc('get_category_hierarchy', {
        category_slug: childSlug
      });

      if (childError) {
        console.error('❌ Child category test failed:', childError);
      } else {
        console.log(`✅ Child category test (${childSlug}): ${childTest?.length || 0} categories`);
        if (childTest && childTest.length > 0) {
          console.log(`   - Is parent category: ${childTest[0].is_parent}`);
        }
      }
    } else {
      console.log('⚠️  No child categories found to test');
    }
    console.log('');

    // Test 4: Performance test
    console.log('4. Performance test...');
    const startTime = Date.now();
    await supabase.rpc('get_category_hierarchy', {
      category_slug: 'home-garden'
    });
    const duration = Date.now() - startTime;
    console.log(`✅ Query completed in ${duration}ms`);
    
    if (duration > 1000) {
      console.warn('⚠️  Query took longer than 1 second - consider optimization');
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testCategoryHierarchy();