# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### **Ultra-Simple Local Development (99% of daily work)**
- `npm run dev` - Start development server (FASTEST - use this for all daily development)
- `npm run clean && npm run dev` - When cache issues occur

### Build and Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run clean` - Remove .next directory
- `npm run clean:build` - Clean and build
- `npm run preview` - Build and start production server

### **Build Testing (when needed)**
- `NODE_ENV=test npm run build && npm run start` - Test production build without authentication barriers
- `npm run clean && NODE_ENV=test npm run build && npm run start` - Clean build testing

### Testing
- `npm run test` - Run all Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:ci` - Run tests in CI mode with simplified config
- `npm run test:camelcase` - Run camelCase validation tests
- `npm run test:api` - Run API tests
- `npm run test:metadata` - Run metadata tests

### Linting and Quality
- `npm run lint` - Run ESLint
- `npx eslint --fix` - Auto-fix ESLint issues

### SEO and Performance
- `npm run seo:test` - Run SEO tests
- `npm run audit:seo` - Run Lighthouse SEO audit
- `npm run audit:performance` - Run Lighthouse performance audit
- `npm run performance:check` - Check Web Vitals

## Project Architecture

### Technology Stack
- **Framework**: Next.js 15.3.5 with App Router (Security upgraded July 2025)
- **Database**: Supabase (PostgreSQL)
- **UI Framework**: React 19.1.0 with TypeScript (Stable release upgrade July 2025)
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Query for server state
- **Authentication**: Supabase Auth
- **Testing**: Jest with React Testing Library
- **Deployment**: AWS Amplify with Cloudflare security

### Key Architectural Patterns

#### 1. Data Layer Architecture
The application uses a centralized data layer pattern located in `src/lib/data/`:
- **Server-side data functions**: All database operations use server-side functions with proper caching
- **Type safety**: Comprehensive TypeScript interfaces in `src/lib/data/types.ts`
- **Caching strategy**: Redis-like caching with `createCachedFunction` utility
- **Supabase client**: Server-side read-only client for security (`src/lib/supabase/server.ts`)

#### 2. URL State Management with usePagination
The `usePagination` hook (`src/hooks/usePagination.ts`) is the **single source of truth** for all paginated URLs:
- **Centralized parameter handling**: All URL parameters including transient ones like `scroll`
- **Page-specific hooks**: `useProductsPagination`, `useRetailersPagination`, `useBrandsPagination`
- **Clean URLs**: Page 1 doesn't show in URL, maintains SEO-friendly structure
- **Browser navigation**: Full support for back/forward navigation

#### 3. Search Architecture
Comprehensive search system with multiple components:
- **Search API**: `/api/search/route.ts` with rate limiting and validation
- **Search data layer**: `src/lib/data/search.ts` with caching and transformation
- **Search components**: `SearchBar`, `SearchSuggestions` in `src/components/search/`
- **Search suggestions**: Auto-complete with brand and product suggestions

#### 4. Security Implementation
Multi-layered security approach with runtime protection:
- **🔒 CRITICAL: Runtime Security Guard-Rails**: Production environment protection in `src/lib/env-guard.ts`
- **HTTP Security Headers**: CSP, HSTS, XSS protection in `next.config.js`
- **Rate Limiting**: API route protection with `src/lib/rateLimiter.ts`
- **Input Validation**: Zod schemas in `src/lib/validation/schemas.ts`
- **DOMPurify**: XSS prevention with isomorphic-dompurify
- **Cloudflare Turnstile**: Bot protection and CAPTCHA

#### 5. Performance Optimization
- **Image optimization**: Next.js Image with multiple remote patterns
- **Caching layers**: Multiple cache durations (SHORT, MEDIUM, EXTENDED)
- **Web Vitals monitoring**: Real-time performance tracking
- **Code splitting**: Webpack optimization for vendor chunks
- **SEO optimization**: Structured data, sitemaps, metadata utils

### Directory Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes with rate limiting
│   ├── brands/            # Brand listing and detail pages
│   ├── products/          # Product listing and detail pages
│   ├── search/            # Search functionality
│   └── layout.tsx         # Root layout with providers
├── components/            # Reusable UI components
│   ├── layout/           # Header, footer, SEO components
│   ├── pages/            # Page-specific client components
│   ├── search/           # Search-related components
│   ├── ui/               # shadcn/ui components
│   └── debug/            # Development debugging tools
├── lib/                   # Core utilities and services
│   ├── data/             # Data layer (products, brands, search)
│   ├── supabase/         # Database clients
│   ├── validation/       # Zod schemas
│   └── security/         # Security utilities
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
```

### Database Schema
Core entities with full-text search capabilities:
- **Products**: With brand, category, promotion relationships
- **Brands**: With search vector and featured status
- **Retailers**: With claim periods and offer management
- **Promotions**: With time-based validation and cashback rules
- **Product_Retailer_Offers**: Price comparison and stock status

### Key Development Patterns

#### 1. Component Architecture
- **Client components**: Use 'use client' directive, located in `src/components/pages/`
- **Server components**: Default for page.tsx files, handle data fetching
- **UI components**: shadcn/ui based, in `src/components/ui/`
- **Layout components**: Header, footer, SEO in `src/components/layout/`

#### 2. Data Fetching Pattern
```typescript
// Always use server-side data functions
import { getProducts } from '@/lib/data/products'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

// In server components
const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)
```

#### 3. URL Management Pattern
```typescript
// Always use pagination hooks for URL state
const { currentPage, goToPage, updateFilters } = useProductsPagination()

// For navigation
goToPage(2) // Handles URL construction and scroll behavior
updateFilters({ brand: 'samsung' }) // Resets to page 1
```

#### 4. Error Handling
- **API routes**: Use try-catch with structured error responses
- **Components**: Error boundaries and fallback UI
- **Validation**: Zod schema validation with detailed error messages
- **Monitoring**: Debug logging in development mode

### Environment Variables
Key environment variables (see `docs/ENVIRONMENT_VARIABLES.md`):
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Public Supabase key
- `SUPABASE_SERVICE_ROLE_KEY` - Server-side Supabase key
- `NEXT_PUBLIC_TURNSTILE_SITE_KEY` - Cloudflare Turnstile
- `TURNSTILE_SECRET_KEY` - Turnstile server validation

### Testing Strategy
- **Unit tests**: Jest with React Testing Library
- **Integration tests**: API route testing with Supabase
- **End-to-end tests**: Playwright for critical user flows
- **Security tests**: XSS, rate limiting, input validation tests
- **Performance tests**: Web Vitals and Lighthouse audits

### Deployment and Infrastructure
- **Hosting**: Our production build is deployed via AWS Amplify Console
- **Database**: Supabase with migrations in `supabase/migrations/`
- **CDN**: Cloudflare for security headers and bot protection
- **Images**: Next.js Image optimization with remote patterns
- **Monitoring**: Web Vitals and error tracking
- # INFRASTRUCTURE:
Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 

## Important Notes

### Security Requirements
- **🔒 CRITICAL: Runtime Security Protection**: The application uses `src/lib/env-guard.ts` to prevent test-only bypass flags in production
- **🔒 CRITICAL: Supply Chain Security**: All CI dependencies are version-pinned (wait-on@8.0.3, @lhci/cli@0.15.x) to prevent malicious updates
- **🔒 CRITICAL: Secrets Protection**: GitHub Actions workflows use ::add-mask:: to prevent accidental credential exposure in logs
- **🔒 Multi-layered Security Scanning**: Build artifacts are scanned for test flags and credential leakage before deployment
- Never expose the service role key in client-side code
- Always validate user inputs with Zod schemas
- Use DOMPurify for any dynamic HTML content
- Implement rate limiting on all API routes
- Follow CSP guidelines for script and style sources

### Performance Guidelines
- Use server-side data functions with proper caching
- Implement lazy loading for images and components
- Monitor Web Vitals and maintain good Core Web Vitals scores
- Use appropriate cache headers for API responses

### Development Workflow
1. Run `npm run dev` for development server
2. Use `npm run lint` before committing
3. Run `npm run test` to ensure all tests pass
4. Use `npm run build` to verify production build
5. Check `npm run audit:seo` for SEO compliance

### Common Issues and Solutions
- **Security guard-rail failures**: Check environment variables for test-only bypass flags in production
- **CI dependency issues**: Verify pinned versions (wait-on@8.0.3, @lhci/cli@0.15.x) in workflow files
- **Secrets logging**: Ensure ::add-mask:: is applied before any environment variable usage in CI
- **Build security failures**: Check for test flags or real credentials accidentally included in .next/ artifacts
- **Build errors**: Run `npm run clean:build` to clear cache
- **Type errors**: Check `src/lib/data/types.ts` for interface definitions
- **Pagination issues**: Always use the `usePagination` hook family
- **Search not working**: Verify API route and data layer functions
- **Security headers**: Check `next.config.js` CSP configuration

### 📚 New Comprehensive Development Guides

For detailed guidance on specific development tasks, refer to these new comprehensive documentation files:

#### **Development & Setup Guides**
- [`docs/development/LOCAL_DEVELOPMENT_GUIDE.md`](docs/development/LOCAL_DEVELOPMENT_GUIDE.md) - **Complete local development commands and workflows**
  - Daily development commands with cache clearing
  - Production testing locally with security bypasses
  - Environment-specific configurations and troubleshooting
- [`docs/development/ENVIRONMENT_SETUP.md`](docs/development/ENVIRONMENT_SETUP.md) - **Environment variables configuration for all environments**
  - Development, test, staging, and production configurations
  - Service-specific setup (Supabase, Sentry, Cloudflare)
  - Security requirements and best practices
- [`docs/development/SECURITY_GUARD_RAILS.md`](docs/development/SECURITY_GUARD_RAILS.md) - **Understanding and working with security guard-rails**
  - How security protection works and when it activates
  - Bypass methods for local development and testing
  - Debugging security violations and customization options
- [`docs/development/BUILD_TROUBLESHOOTING.md`](docs/development/BUILD_TROUBLESHOOTING.md) - **Comprehensive build and deployment troubleshooting**
  - Security violations, memory errors, TypeScript issues
  - Server problems, authentication errors, cache issues
  - Performance optimization and emergency procedures

#### **Deployment & Infrastructure Guides**
- [`docs/deployment/AWS_AMPLIFY_SETUP.md`](docs/deployment/AWS_AMPLIFY_SETUP.md) - **Complete AWS Amplify hosting setup and configuration**
  - Amplify app creation, build settings, environment variables
  - Domain setup with Cloudflare integration
  - Security headers, monitoring, and performance optimization

#### **Quick Reference Commands**

**Daily Development Workflow:**
```bash
# Fresh start for development (most common)
npm run clean && npm run dev

# Production testing with authentication disabled
NODE_ENV=test npm run build && npm run start

# Complete reset when issues persist
rm -rf .next node_modules && npm install && npm run dev
```

**Emergency Troubleshooting:**
```bash
# Security guard-rail bypass
NODE_ENV=test npm run build

# Kill processes on port 3000  
lsof -ti:3000 | xargs kill -9

# Memory issues
NODE_OPTIONS="--max-old-space-size=4096" npm run build

# Nuclear reset
rm -rf .next node_modules package-lock.json && npm install
```

**See the individual documentation files for comprehensive step-by-step guidance on each topic.**

## Memory: Comprehensive Documentation Suite

I have created a complete documentation suite for the Cashback Deals v2 codebase to assist with onboarding and development. Each file provides detailed technical guidance for specific aspects of the system.

### Documentation Files Summary & Links

### 📚 **Comprehensive Documentation Suite**

All technical documentation is organized in the [`docs/`](docs/) directory with logical categorization:

#### **Technical Architecture** ([docs/technical/](docs/technical/))
- [**ARCHITECTURE.md**](docs/technical/ARCHITECTURE.md) - System architecture, technology stack, design patterns
- [**DATA_MODEL.md**](docs/technical/DATA_MODEL.md) - Database schema, relationships, caching strategies  
- [**SECURITY.md**](docs/technical/SECURITY.md) - Multi-layered security implementation and guidelines

#### **Development Workflows** ([docs/development/](docs/development/))
- [**WORKFLOWS.md**](docs/development/WORKFLOWS.md) - Developer onboarding, Git workflows, IDE setup
- [**TESTING.md**](docs/development/TESTING.md) - Testing strategies, environment setup, best practices
- [**TROUBLESHOOTING.md**](docs/development/TROUBLESHOOTING.md) - Common issues, solutions, debugging procedures

#### **Deployment & Infrastructure** ([docs/deployment/](docs/deployment/))
- [**CI_CD.md**](docs/deployment/CI_CD.md) - GitHub Actions workflows, AWS Amplify deployment
- [**DEPLOYMENT_GUIDE.md**](docs/deployment/DEPLOYMENT_GUIDE.md) - Multi-platform deployment procedures

#### **Reference Materials** ([docs/reference/](docs/reference/))
- [**COMPONENT_DEPENDENCY_MATRIX.md**](docs/reference/COMPONENT_DEPENDENCY_MATRIX.md) - Page-to-component mapping
- [**LIBRARIES_AND_UTILITIES.md**](docs/reference/LIBRARIES_AND_UTILITIES.md) - Package dependencies and utilities

#### **Performance & SEO** ([docs/performance/](docs/performance/))
- [**PERFORMANCE_SEO.md**](docs/performance/PERFORMANCE_SEO.md) - Core Web Vitals, SEO strategies, optimization

### 🎯 **Quick Navigation Guide**

**For Architecture Questions**: Use [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) for system design and patterns
**For Database Work**: Use [`technical/DATA_MODEL.md`](docs/technical/DATA_MODEL.md) for schema and relationships
**For New Developer Setup**: Use [`development/WORKFLOWS.md`](docs/development/WORKFLOWS.md) for onboarding
**For Testing Issues**: Use [`development/TESTING.md`](docs/development/TESTING.md) for test setup and debugging
**For Security Concerns**: Use [`technical/SECURITY.md`](docs/technical/SECURITY.md) for authentication and security
**For Deployment Issues**: Use [`deployment/CI_CD.md`](docs/deployment/CI_CD.md) and [`deployment/DEPLOYMENT_GUIDE.md`](docs/deployment/DEPLOYMENT_GUIDE.md)
**For Performance Problems**: Use [`performance/PERFORMANCE_SEO.md`](docs/performance/PERFORMANCE_SEO.md) for optimization
**For Debugging**: Use [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md) for solutions
**For Dependencies**: Use [`reference/LIBRARIES_AND_UTILITIES.md`](docs/reference/LIBRARIES_AND_UTILITIES.md) for packages
**For Component Mapping**: Use [`reference/COMPONENT_DEPENDENCY_MATRIX.md`](docs/reference/COMPONENT_DEPENDENCY_MATRIX.md)

### 🚀 **Quick Reference for Common Tasks**

- **Adding new features**: [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) → [`technical/DATA_MODEL.md`](docs/technical/DATA_MODEL.md) → [`development/TESTING.md`](docs/development/TESTING.md)
- **Fixing bugs**: [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md) first, then relevant technical docs
- **Performance issues**: [`performance/PERFORMANCE_SEO.md`](docs/performance/PERFORMANCE_SEO.md) and [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md)
- **Security concerns**: [`technical/SECURITY.md`](docs/technical/SECURITY.md) and [`development/TESTING.md`](docs/development/TESTING.md) security sections
- **Deployment problems**: [`deployment/DEPLOYMENT_GUIDE.md`](docs/deployment/DEPLOYMENT_GUIDE.md) → [`deployment/CI_CD.md`](docs/deployment/CI_CD.md) → [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md)
- **New developer onboarding**: [`development/WORKFLOWS.md`](docs/development/WORKFLOWS.md) → [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) → [`docs/README.md`](docs/README.md)

## Memory: Security Upgrade - Next.js 15.3.5 + React 19.1.0

**Date:** July 9, 2025  
**Status:** ✅ Successfully Completed

### Major Framework Security Upgrade
Successfully completed comprehensive security upgrade from Next.js 15.1.4 → 15.3.5 and React 19.0.0 → 19.1.0 with all runtime issues resolved and UI functionality maintained.

**Key Changes:**
- **Security patches** applied via Next.js 15.3.5
- **React 19.1.0 stable release** with performance improvements
- **TailwindCSS 4.x compatibility** via @tailwindcss/postcss
- **Enhanced Content Security Policy** for Sentry integration
- **Fixed React 19.1.0 Image component warnings**

**Files Modified:**
- `package.json` - Framework version updates
- `postcss.config.mjs` - TailwindCSS 4.x PostCSS plugin fix
- `tailwind.config.ts` - Fixed darkMode TypeScript syntax
- `next.config.js` - Enhanced CSP for Sentry domains + worker-src
- `src/components/FeaturedProductCard.tsx` - Fixed Image positioning
- `jest.config.js` - Excluded Playwright tests from Jest

**New Infrastructure:**
- `.github/workflows/ci.yml` - CI pipeline with Node 18.x/20.x/22.x matrix
- `amplify.yml` - AWS Amplify deployment configuration

**Verification Results:**
- ✅ UI renders correctly with all sections displaying
- ✅ No CSP violations in console logs
- ✅ Data fetching works (Products, Promotions, Brands, Retailers)
- ✅ Server stable on Next.js 15.3.5 + React 19.1.0
- ✅ All tests pass and build succeeds

**📋 Complete Details:** See [SECURITY_UPGRADE_CHANGELOG.md](SECURITY_UPGRADE_CHANGELOG.md) for comprehensive documentation of all changes, rollback procedures, and security impact analysis.

**Next Actions:**
- Monitor production for any runtime issues
- Plan Node.js 22.x migration before Sep 15, 2025
- Set up automated security scanning for future vulnerabilities