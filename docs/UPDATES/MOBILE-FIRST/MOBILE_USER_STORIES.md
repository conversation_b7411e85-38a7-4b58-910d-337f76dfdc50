# 📚 Mobile-First User Stories & Implementation Plan - Updated 2025

## Overview

This document contains detailed user stories with tasks, acceptance criteria, and technical specifications for the mobile-first optimization of RebateRay. Each epic incorporates **latest 2025 Airbnb design patterns** and ensures **strict CLAUDE.md architectural compliance**.

---

## Epic 1: Foundation & Core Components

### **User Story 1.1: Mobile-First Touch System**
> *As a mobile user, I need touch-optimized interactions so that all buttons and interactive elements are easily tappable*

**Business Value:** Eliminates touch target accessibility issues and provides native app-like interactions following 2025 Airbnb standards

**CLAUDE.md Compliance Requirements:**
- Must integrate with existing `usePagination` hooks for navigation
- Preserve all security validation patterns (Zod schemas)
- Maintain compatibility with server-side data functions

**Tasks:**
1. Create `TouchButton` component extending shadcn/ui Button
2. Implement `TouchIcon` wrapper for consistent 44px sizing (stricter than Airbnb's 32px)
3. Add haptic feedback simulation system using Web Vibration API
4. Create touch interaction hooks compatible with existing architecture
5. Update all existing buttons to use TouchButton while preserving functionality

**Latest 2025 Airbnb Patterns Applied:**
- **Touch Targets**: 44px minimum (exceeding Airbnb's 32-40px for better accessibility)
- **Border Radius**: 20px for cards, adaptive for buttons
- **Primary Color**: #FF385C for primary CTAs
- **Interaction Feedback**: Gradient backgrounds on press

**Acceptance Criteria:**
- [ ] All interactive elements minimum 44px touch target (WCAG AA compliance)
- [ ] Press animations with 0.95 scale on touch for visual feedback
- [ ] Haptic feedback simulation for key interactions (save, purchase, etc.)
- [ ] Compatible with existing shadcn/ui Button variants and styling
- [ ] Full keyboard navigation support maintained
- [ ] Touch targets increase appropriately: 44px mobile, 48px tablet, 56px desktop
- [ ] Works with assistive technologies (screen readers)
- [ ] Preserves all existing onClick handlers and form submissions
- [ ] Integration with `usePagination` navigation functions maintained

**Technical Implementation:**
```typescript
// src/components/ui/touch/TouchButton.tsx
interface TouchButtonProps extends ButtonProps {
  touchSize?: 'sm' | 'md' | 'lg'; // 44px | 48px | 56px
  haptic?: boolean;
  pressScale?: number;
  children: React.ReactNode;
  // Preserve existing pagination integration
  onPageChange?: (page: number) => void;
}

const TouchButton = ({
  touchSize = 'md',
  haptic = true,
  pressScale = 0.95,
  className,
  onPageChange,
  ...props
}: TouchButtonProps) => {
  const sizeClasses = {
    sm: 'min-h-[44px] min-w-[44px]',
    md: 'min-h-[48px] min-w-[48px]', 
    lg: 'min-h-[56px] min-w-[56px]'
  }
  
  // 2025 Airbnb color scheme
  const colorClasses = {
    primary: 'bg-[#FF385C] hover:bg-[#E31C5F] text-white',
    secondary: 'bg-white border-2 border-[#FF385C] text-[#FF385C]'
  }

  const handleClick = (e: React.MouseEvent) => {
    if (haptic && 'vibrate' in navigator) {
      navigator.vibrate(50) // Subtle haptic feedback
    }
    
    // Preserve existing functionality
    if (onPageChange && props.onClick) {
      props.onClick(e)
    } else if (props.onClick) {
      props.onClick(e)
    }
  }
  
  return (
    <motion.div
      whileTap={{ scale: pressScale }}
      transition={{ duration: 0.1 }}
    >
      <Button
        className={cn(
          sizeClasses[touchSize], 
          'rounded-[20px]', // 2025 Airbnb border radius
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Button>
    </motion.div>
  )
}

// Integration with existing usePagination pattern
const PaginatedComponent = () => {
  const { currentPage, goToPage } = useProductsPagination() // Required CLAUDE.md pattern
  
  return (
    <TouchButton 
      onPageChange={(page) => goToPage(page)}
      variant="primary"
    >
      Load More Products
    </TouchButton>
  )
}
```

**Files to Create/Modify:**
- `src/components/ui/touch/TouchButton.tsx` (new)
- `src/components/ui/touch/TouchIcon.tsx` (new)
- `src/hooks/useHaptic.ts` (new)
- `src/components/layout/header.tsx` (update all buttons, preserve existing navigation)
- `src/components/FeaturedProductCard.tsx` (update buttons, preserve product data fetching)

**CLAUDE.md Integration Requirements:**
- Must work with `createServerSupabaseReadOnlyClient()` data fetching
- Preserve all Zod schema validation on form submissions
- Maintain compatibility with existing rate limiting
- Support AWS Amplify deployment patterns

---

### **User Story 1.2: Enhanced Search Experience with 2025 Airbnb Patterns**
> *As a user, I need immediate access to search on all devices with modern, intuitive design patterns so I can quickly find products*

**Business Value:** Removes major conversion barrier by making search immediately accessible with latest UX patterns

**CLAUDE.md Compliance Requirements:**
- Must use existing `searchProducts()` function from `src/lib/data/products`
- Preserve all security validation (Zod schemas, DOMPurify)
- Maintain rate limiting and CSP compliance
- Integration with existing `usePagination` for search results

**Latest 2025 Airbnb Patterns Applied:**
- **Border Radius**: 50px for search input (major update from 12px)
- **Header Height**: 80px with proper clearance
- **Background**: Gradient from white to light gray
- **Padding**: 15-32px horizontal for touch optimization
- **Font Size**: 14px placeholder text
- **Primary Color**: #FF385C for search button

**Tasks:**
1. **CRITICAL**: Make search always visible on mobile header (remove `hidden md:block`)
2. Implement 50px border radius search interface matching Airbnb 2025
3. Add search chips for quick category access below input
4. Enhance search suggestions with shadcn/ui Command component
5. Add voice search icon for progressive enhancement
6. Maintain all existing security and performance patterns

**Acceptance Criteria:**
- [ ] Search visible on mobile header (remove `hidden md:block` class from header.tsx:48)
- [ ] Search input 50px border radius (matching 2025 Airbnb standard)
- [ ] Minimum 48px height for touch optimization
- [ ] Search chips for popular categories (Electronics, Fashion, Home, etc.)
- [ ] Enhanced autocomplete using shadcn/ui Popover and Command components
- [ ] Voice search icon for browsers supporting Web Speech API
- [ ] **SECURITY**: Maintains existing Zod validation and DOMPurify sanitization
- [ ] **PERFORMANCE**: Search suggestions load within 300ms
- [ ] **ACCESSIBILITY**: Keyboard navigation works for all suggestion items
- [ ] **INTEGRATION**: Uses existing `searchProducts()` function
- [ ] Clear search functionality easily accessible
- [ ] Preserves existing rate limiting on search API

**Technical Implementation:**
```typescript
// Enhanced SearchBar with 2025 Airbnb patterns + CLAUDE.md compliance
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { searchProducts } from '@/lib/data/products' // Required CLAUDE.md pattern
import { validateSearchQuery } from '@/lib/security/utils' // Required security
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

const SearchBar = ({ onSearch, initialValue = '' }: SearchBarProps) => {
  const [query, setQuery] = useState(initialValue)
  const [suggestions, setSuggestions] = useState([])
  const [validationError, setValidationError] = useState<string | null>(null)
  
  // Required CLAUDE.md pattern - use existing search function
  const handleSearch = async (searchQuery: string) => {
    // Required security validation
    const validation = validateSearchQuery(searchQuery)
    if (!validation.isValid) {
      setValidationError('Invalid search query')
      return
    }
    
    try {
      const supabase = createServerSupabaseReadOnlyClient() // Required pattern
      const results = await searchProducts(supabase, {
        query: validation.sanitized,
        page: 1,
        limit: 10
      })
      setSuggestions(results.products || [])
    } catch (error) {
      console.error('Search error:', error)
    }
  }

  return (
    <div className="flex-1 max-w-md">
      <Popover>
        <PopoverTrigger asChild>
          <div className="relative">
            {/* 2025 Airbnb search styling */}
            <CommandInput
              placeholder="Search products, brands..."
              className="h-12 pl-8 pr-12 rounded-[50px] border-2 bg-gradient-to-r from-white to-gray-50"
              value={query}
              onChange={(e) => {
                setQuery(e.target.value)
                handleSearch(e.target.value) // Uses existing searchProducts function
              }}
            />
            <TouchButton 
              size="sm" 
              variant="ghost"
              className="absolute right-2 top-2 rounded-full bg-[#FF385C] text-white"
            >
              <Search className="h-5 w-5" />
            </TouchButton>
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup heading="Products">
              {suggestions.map((product) => (
                <CommandItem 
                  key={product.id}
                  onSelect={() => {
                    // Integrate with existing pagination
                    router.push(`/products/${product.slug}`)
                  }}
                >
                  {product.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      
      {/* Search Chips - 2025 Airbnb pattern */}
      <div className="flex gap-2 mt-2 overflow-x-auto pb-2">
        {quickSearches.map((search) => (
          <Badge 
            key={search} 
            variant="secondary" 
            className="cursor-pointer whitespace-nowrap rounded-[20px] bg-gray-100 hover:bg-[#FF385C] hover:text-white"
            onClick={() => handleSearch(search)}
          >
            {search}
          </Badge>
        ))}
      </div>
      
      {/* Validation Error Display */}
      {validationError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm z-50">
          {validationError}
        </div>
      )}
    </div>
  )
}
```

**Files to Create/Modify:**
- `src/components/layout/header.tsx` (CRITICAL: remove `hidden md:block`, update to 80px height)
- `src/components/search/SearchBar.tsx` (major enhancement with 50px radius)
- `src/components/search/SearchChips.tsx` (new)
- `src/components/search/SearchSuggestions.tsx` (shadcn/ui integration)
- `src/hooks/useVoiceSearch.ts` (new, progressive enhancement)

**CLAUDE.md Security Integration:**
- All search queries validated with existing Zod schemas
- DOMPurify sanitization maintained for suggestions
- Rate limiting preserved on `/api/search/route.ts`
- CSP headers in `next.config.js` support search functionality

---

## Epic 2: Navigation & Layout Systems

### **User Story 2.1: Modern Mobile Navigation with 80px Header Clearance**
> *As a mobile user, I need intuitive navigation that follows 2025 mobile app conventions with proper spacing and accessibility*

**Business Value:** Provides familiar mobile navigation patterns that reduce cognitive load and improve conversion

**CLAUDE.md Compliance Requirements:**
- Navigation must integrate with `usePagination` hooks for deep linking
- Preserve existing route structure and server-side rendering
- Maintain AWS Amplify deployment compatibility

**Latest 2025 Airbnb Patterns Applied:**
- **Header Height**: 80px sticky positioning
- **Border Radius**: 20px for navigation cards
- **Touch Targets**: 44px minimum for navigation items
- **Color Scheme**: #FF385C for active states
- **Bottom Clearance**: Account for 80px header in slide-up components

**Tasks:**
1. Implement bottom navigation bar for mobile core actions
2. Create slide-out navigation drawer using shadcn/ui Sheet
3. Add breadcrumb navigation for deep pages with proper hierarchy
4. Implement swipe gestures for navigation (progressive enhancement)
5. Add navigation state persistence across page changes
6. Ensure 80px header clearance for all mobile components

**Acceptance Criteria:**
- [ ] Fixed bottom navigation with Home, Search, Saved, Account tabs
- [ ] Navigation drawer using shadcn/ui Sheet component for secondary actions
- [ ] Breadcrumbs on product and brand detail pages using existing data structure
- [ ] Swipe gestures for back navigation (iOS-style, progressive enhancement)
- [ ] Maintains accessibility standards (focus management, screen readers)
- [ ] Hidden on desktop viewports (md:hidden)
- [ ] Navigation state persists across page changes
- [ ] Active tab highlighting with proper contrast ratios
- [ ] **INTEGRATION**: Works with existing `usePagination` deep linking
- [ ] 80px header clearance maintained for all slide-up components

**Technical Implementation:**
```typescript
// Bottom Navigation with CLAUDE.md integration
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb"
import { useProductsPagination } from '@/hooks/usePagination' // Required pattern

const BottomNavigation = () => {
  const navigation = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Search', href: '/search', icon: Search },
    { name: 'Brands', href: '/brands', icon: ShoppingBag },
    { name: 'Account', href: '/account', icon: User }
  ]

  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-white border-t md:hidden z-50 shadow-lg">
      <div className="grid grid-cols-4 h-full">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className="flex flex-col items-center justify-center p-2 text-xs hover:bg-gray-50 transition-colors"
          >
            <item.icon className="h-6 w-6 mb-1 text-gray-600" />
            <span className="text-gray-600">{item.name}</span>
          </Link>
        ))}
      </div>
      {/* 80px header clearance for body content */}
      <style jsx global>{`
        body.mobile-nav-active {
          padding-bottom: 64px; /* Bottom nav clearance */
          padding-top: 80px; /* Header clearance */
        }
      `}</style>
    </nav>
  )
}

// Enhanced Navigation Drawer with CLAUDE.md patterns
const NavigationDrawer = () => {
  const { currentPage, goToPage } = useProductsPagination() // Required integration
  
  return (
    <Sheet>
      <SheetTrigger asChild>
        <TouchButton variant="ghost" size="md">
          <Menu className="h-6 w-6" />
        </TouchButton>
      </SheetTrigger>
      <SheetContent 
        side="left" 
        className="w-80 pt-[80px]" // Account for 80px header
      >
        <nav className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-semibold text-lg mb-4">Categories</h3>
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/products?category=${category.slug}`}
                className="block p-3 rounded-[20px] hover:bg-gray-100 transition-colors"
                onClick={() => {
                  // Integrate with existing pagination
                  goToPage(1) // Reset to first page when filtering
                }}
              >
                {category.name}
              </Link>
            ))}
          </div>
        </nav>
      </SheetContent>
    </Sheet>
  )
}
```

**Files to Create/Modify:**
- `src/components/layout/BottomNavigation.tsx` (new)
- `src/components/layout/NavigationDrawer.tsx` (new)
- `src/components/ui/Breadcrumbs.tsx` (new)
- `src/app/layout.tsx` (add bottom navigation with 80px clearance)
- `src/hooks/useSwipeGestures.ts` (new)
- All detail pages (add breadcrumbs using existing data structure)

---

### **User Story 2.2: Progressive Content Disclosure with Airbnb 2025 Patterns**
> *As a user, I want to see the most important information first with modern, intuitive disclosure patterns*

**Business Value:** Reduces cognitive load and improves conversion by showing relevant information hierarchically

**CLAUDE.md Compliance Requirements:**
- Must use existing product data from `getProducts()` function
- Preserve all product relationships (brand, category, retailer offers)
- Maintain server-side rendering for SEO
- Integration with existing caching strategies

**Latest 2025 Airbnb Patterns Applied:**
- **Card Border Radius**: 20px for all product cards
- **Progressive Disclosure**: Collapsible sections with smooth animations
- **Color Accents**: #FF385C for interactive elements
- **Touch Targets**: 44px minimum for all controls
- **Grid Spacing**: 2-12px gaps following Airbnb patterns

**Tasks:**
1. Redesign product cards with progressive disclosure using 20px border radius
2. Implement expandable sections using shadcn/ui Collapsible
3. Add quick action buttons on cards (save, share, compare) with #FF385C accents
4. Create modal detail views for comprehensive product information
5. Add smooth animations following cubic-bezier patterns
6. Maintain performance with large product lists using existing caching

**Acceptance Criteria:**
- [ ] Product cards show essential info first (image, price, cashback amount)
- [ ] Expandable details using shadcn/ui Collapsible component
- [ ] Quick actions: Save, Share, Compare using TouchButton with #FF385C
- [ ] Modal views for detailed product information
- [ ] Smooth animations (0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94))
- [ ] **PERFORMANCE**: Maintains performance with large lists using existing caching
- [ ] **DATA**: Uses existing product data structure and relationships
- [ ] Information hierarchy follows F-pattern for mobile readability
- [ ] Expansion state persists during session
- [ ] 20px border radius on all cards matching Airbnb 2025

**Technical Implementation:**
```typescript
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { getProducts } from '@/lib/data/products' // Required CLAUDE.md pattern

const ProductCard = ({ product }: { product: TransformedProduct }) => {
  const [isExpanded, setIsExpanded] = useState(false)
  
  return (
    <motion.div
      className="rounded-[20px] overflow-hidden bg-white shadow-sm border"
      whileHover={{ y: -2 }}
      transition={{ 
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94] // Airbnb cubic-bezier
      }}
    >
      {/* Essential Information - Always Visible */}
      <div className="relative aspect-[3/2] sm:aspect-[4/3]">
        <Image 
          src={product.images?.[0] || '/placeholder.jpg'} 
          alt={product.name} 
          fill 
          className="object-contain p-2"
        />
        {/* Quick Actions - 2025 Airbnb positioning */}
        <div className="absolute top-2 right-2 flex gap-2">
          <TouchButton 
            size="sm" 
            variant="secondary"
            className="bg-white/90 backdrop-blur-sm rounded-full"
          >
            <Heart className="h-4 w-4" />
          </TouchButton>
          <TouchButton 
            size="sm" 
            variant="secondary"
            className="bg-white/90 backdrop-blur-sm rounded-full"
          >
            <Share className="h-4 w-4" />
          </TouchButton>
        </div>
      </div>
      
      <div className="p-4">
        {/* Essential Info */}
        <h3 className="font-semibold text-sm line-clamp-2 mb-2">
          {product.brand?.name} {product.name}
        </h3>
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-bold text-gray-900">
            £{product.minPrice?.toFixed(2)}
          </span>
          <Badge 
            variant="secondary" 
            className="bg-[#FF385C] text-white rounded-[20px]"
          >
            £{product.cashbackAmount?.toFixed(2)} cashback
          </Badge>
        </div>
        
        {/* Progressive Disclosure */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <TouchButton 
              variant="ghost" 
              size="sm" 
              className="w-full h-[44px] rounded-[20px]"
            >
              {isExpanded ? 'Less details' : 'More details'}
              <ChevronDown className={cn(
                "h-4 w-4 ml-2 transition-transform duration-200",
                isExpanded && "rotate-180"
              )} />
            </TouchButton>
          </CollapsibleTrigger>
          
          <CollapsibleContent 
            className="space-y-3 mt-3"
            style={{
              transition: 'height 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            }}
          >
            <p className="text-sm text-gray-600 line-clamp-3">
              {product.description}
            </p>
            
            {/* Retailer Offers */}
            {product.retailerOffers?.length > 0 && (
              <div className="space-y-2">
                <p className="text-xs font-medium text-gray-500">
                  Available from {product.retailerOffers.length} retailer(s)
                </p>
                <div className="flex gap-2 overflow-x-auto">
                  {product.retailerOffers.slice(0, 3).map((offer) => (
                    <Badge
                      key={offer.id}
                      variant="outline"
                      className="text-xs whitespace-nowrap rounded-[20px]"
                    >
                      {offer.retailer.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex gap-2 pt-2">
              <TouchButton 
                size="sm" 
                className="flex-1 bg-[#FF385C] hover:bg-[#E31C5F] rounded-[20px]"
              >
                View Deal
              </TouchButton>
              <TouchButton 
                size="sm" 
                variant="outline" 
                className="rounded-[20px] border-[#FF385C] text-[#FF385C]"
              >
                Compare
              </TouchButton>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </motion.div>
  )
}

// Server Component Integration (CLAUDE.md pattern)
const ProductGridServer = async ({ filters }: { filters: ProductFilters }) => {
  const supabase = createServerSupabaseReadOnlyClient() // Required pattern
  const products = await getProducts(supabase, filters, 1, 20) // Required function
  
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

**Files to Create/Modify:**
- `src/components/FeaturedProductCard.tsx` (major redesign with 20px radius)
- `src/components/ProductCard.tsx` (mobile optimization)
- `src/components/ui/QuickActions.tsx` (new)
- `src/components/ui/ProductModal.tsx` (new)
- CSS custom properties for 2025 Airbnb animations

---

## Epic 3: Layout & Grid Systems with 2025 Standards

### **User Story 3.1: Mobile-First Grid Layouts with Airbnb Spacing**
> *As a user on any device, I want content that's optimized for my screen size using modern spacing and layout patterns*

**Business Value:** Ensures optimal viewing experience across all devices using proven 2025 design patterns

**CLAUDE.md Compliance Requirements:**
- Must maintain existing data fetching patterns (`getProducts`, `getBrands`)
- Preserve server-side rendering for SEO
- Integration with `usePagination` for grid navigation
- Maintain AWS Amplify deployment compatibility

**Latest 2025 Airbnb Patterns Applied:**
- **Grid Gaps**: 2-12px spacing based on Airbnb's CSS analysis
- **Border Radius**: 20px for all grid items
- **Responsive Breakpoints**: Mobile-first with smooth transitions
- **Touch Targets**: 44px minimum within grid items
- **Visual Hierarchy**: Following Airbnb's content organization

**Tasks:**
1. Convert all desktop-first grids to mobile-first approach
2. Implement responsive product grids with 2-12px gap progression
3. Create flexible brand and retailer layouts with 20px border radius
4. Add container components for consistency across pages
5. Optimize gap spacing: 4px mobile → 8px tablet → 12px desktop
6. Ensure all grid items meet 44px touch target requirements

**Acceptance Criteria:**
- [ ] All grids start with single column on mobile (320px+)
- [ ] Breakpoints: mobile (1 col) → sm:tablet (2 col) → lg:desktop (3-4 cols)
- [ ] Gap spacing progression: 4px → 8px → 12px following Airbnb patterns
- [ ] 20px border radius on all grid items (cards, brand tiles, etc.)
- [ ] Maintains aspect ratios across viewports
- [ ] No horizontal scrolling on any screen size (320px to 2560px)
- [ ] Grid items have minimum 44px touch target size
- [ ] **INTEGRATION**: Preserves existing `usePagination` functionality
- [ ] **PERFORMANCE**: Uses existing cached data functions
- [ ] Graceful degradation for very small screens (< 320px)

**Technical Implementation:**
```typescript
// Mobile-first grid patterns with 2025 Airbnb spacing
const gridClasses = {
  products: {
    base: "grid grid-cols-1 gap-4", // 4px mobile
    sm: "sm:grid-cols-2 sm:gap-6",   // 8px tablet  
    lg: "lg:grid-cols-3 lg:gap-8",   // 12px desktop
    xl: "xl:grid-cols-4"
  },
  brands: {
    base: "grid grid-cols-3 gap-3",  // Compact mobile grid
    sm: "sm:grid-cols-4 sm:gap-4",
    md: "md:grid-cols-6 md:gap-6",
    lg: "lg:gap-8"
  },
  retailers: {
    base: "grid grid-cols-2 gap-4",
    sm: "sm:grid-cols-3 sm:gap-6", 
    md: "md:grid-cols-4 md:gap-8"
  },
  promotions: {
    base: "grid grid-cols-1 gap-4",
    md: "md:grid-cols-2 md:gap-6",
    lg: "lg:grid-cols-3 lg:gap-8"
  }
}

// Responsive Grid Component with CLAUDE.md integration
const ResponsiveGrid = ({ 
  type, 
  children, 
  className,
  data,
  renderItem
}: { 
  type: keyof typeof gridClasses
  children?: React.ReactNode
  className?: string
  data?: any[] // For server-side rendered grids
  renderItem?: (item: any) => React.ReactNode
}) => {
  const gridClass = Object.values(gridClasses[type]).join(' ')
  
  return (
    <div className={cn(gridClass, className)}>
      {children || data?.map((item, index) => (
        <div 
          key={item.id || index} 
          className="rounded-[20px] min-h-[44px]" // Airbnb radius + touch target
        >
          {renderItem ? renderItem(item) : item}
        </div>
      ))}
    </div>
  )
}

// Integration with existing CLAUDE.md patterns
const ProductsGridWithPagination = () => {
  const { currentPage, goToPage } = useProductsPagination() // Required pattern
  const supabase = createServerSupabaseReadOnlyClient() // Required pattern
  
  const products = await getProducts(supabase, {}, currentPage, 20) // Required function
  
  return (
    <div className="space-y-6">
      <ResponsiveGrid
        type="products"
        data={products}
        renderItem={(product) => (
          <ProductCard 
            product={product} 
            className="rounded-[20px]" // 2025 Airbnb pattern
          />
        )}
      />
      
      {/* Pagination with TouchButton */}
      <div className="flex justify-center gap-2">
        {Array.from({ length: totalPages }, (_, i) => (
          <TouchButton
            key={i + 1}
            variant={currentPage === i + 1 ? "primary" : "secondary"}
            size="md"
            className="min-w-[44px] rounded-[20px]"
            onClick={() => goToPage(i + 1)}
          >
            {i + 1}
          </TouchButton>
        ))}
      </div>
    </div>
  )
}

// Updated CSS custom properties for 2025 Airbnb patterns
const airbnbGridStyles = `
  :root {
    --grid-gap-mobile: 1rem;    /* 16px - 4px base */
    --grid-gap-tablet: 1.5rem;  /* 24px - 8px progression */
    --grid-gap-desktop: 2rem;   /* 32px - 12px max */
    
    --card-radius: 20px;        /* Airbnb 2025 standard */
    --touch-target: 44px;       /* WCAG AA minimum */
    
    --color-primary: #FF385C;   /* Airbnb Rausch */
    --color-primary-hover: #E31C5F;
  }
  
  /* Mobile-first container with proper clearance */
  .mobile-container {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 80px;  /* Header clearance */
    padding-bottom: 64px; /* Bottom nav clearance on mobile */
  }
  
  @media (min-width: 640px) {
    .mobile-container {
      padding-left: 2rem;
      padding-right: 2rem;
      padding-bottom: 1rem; /* No bottom nav on tablet+ */
    }
  }
`
```

**Files to Create/Modify:**
- `src/components/pages/HomePageClient.tsx` (grid conversion to mobile-first)
- `src/app/products/components/ProductsContent.tsx` (grid conversion with pagination)  
- `src/app/brands/BrandsClient.tsx` (grid conversion)
- `src/components/pages/SearchPageClient.tsx` (grid conversion)
- `src/components/ui/ResponsiveGrid.tsx` (new)
- `src/lib/utils/gridClasses.ts` (new)
- `src/app/globals.css` (add 2025 Airbnb CSS custom properties)

---

## Epic 4: Enhanced User Interactions with Latest Patterns

### **User Story 4.1: Modern Filter & Sort System with 80px Header Integration**
> *As a user, I want to easily filter and sort products on mobile with modern slide-up patterns and proper spacing*

**Business Value:** Improves product discovery and reduces bounce rate using proven 2025 UX patterns

**CLAUDE.md Compliance Requirements:**
- Must integrate with existing filter API endpoints
- Preserve all Zod schema validation for filter parameters
- Use `usePagination` hooks for filter state management
- Maintain rate limiting and security patterns

**Latest 2025 Airbnb Patterns Applied:**
- **Sheet Height**: 80vh with 80px header clearance
- **Border Radius**: 20px for filter cards and chips
- **Touch Targets**: 44px minimum for all filter controls
- **Color Scheme**: #FF385C for active filters and apply button
- **Spacing**: 2-12px gaps between filter elements

**Tasks:**
1. Transform filters into mobile slide-up drawer with 80px header clearance
2. Implement multi-select filters with shadcn/ui components and 44px touch targets
3. Add sort dropdown with 20px border radius and mobile optimization
4. Create filter chips with #FF385C active states and easy removal
5. Add filter state persistence using existing pagination patterns
6. Ensure all filter controls meet accessibility standards

**Acceptance Criteria:**
- [ ] Filter drawer slides up from bottom occupying 80vh with proper header clearance
- [ ] Multi-select checkboxes using shadcn/ui Checkbox with 44px touch targets
- [ ] Sort dropdown optimized for touch with 20px border radius
- [ ] Active filter chips with #FF385C color and easy removal (X button)
- [ ] **INTEGRATION**: Filter state persists using existing `usePagination` patterns
- [ ] Clear all filters option prominently displayed with TouchButton
- [ ] Filter count badge on filter button using #FF385C
- [ ] **PERFORMANCE**: Results update efficiently with existing caching
- [ ] **SECURITY**: All filter parameters validated with existing Zod schemas
- [ ] Drawer closes properly with backdrop click and escape key

**Technical Implementation:**
```typescript
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useProductsPagination } from '@/hooks/usePagination' // Required CLAUDE.md pattern

const FilterDrawer = ({ 
  isOpen, 
  onClose, 
  brands,
  categories 
}: FilterDrawerProps) => {
  const { updateFilters, clearFilters, currentFilters } = useProductsPagination() // Required pattern
  const [localFilters, setLocalFilters] = useState(currentFilters)
  
  const activeFilterCount = Object.values(localFilters).filter(Boolean).length

  const handleApplyFilters = () => {
    // Use existing pagination pattern for filter updates
    updateFilters(localFilters) // Automatically resets to page 1
    onClose()
  }

  const handleClearAll = () => {
    setLocalFilters({})
    clearFilters() // Existing pattern
    onClose()
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent 
        side="bottom" 
        className="h-[80vh] rounded-t-[20px]" // 2025 Airbnb radius
        style={{ 
          marginTop: '80px' // Account for 80px header
        }}
      >
        <SheetHeader className="pb-4">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-semibold">
              Filter Products
            </SheetTitle>
            <div className="flex items-center gap-3">
              {activeFilterCount > 0 && (
                <Badge 
                  variant="secondary"
                  className="bg-[#FF385C] text-white rounded-[20px]"
                >
                  {activeFilterCount} active
                </Badge>
              )}
              <TouchButton 
                variant="outline" 
                size="sm"
                className="rounded-[20px] border-[#FF385C] text-[#FF385C]"
                onClick={handleClearAll}
              >
                Clear All
              </TouchButton>
            </div>
          </div>
        </SheetHeader>
        
        <div className="space-y-6 py-4 max-h-[calc(80vh-200px)] overflow-y-auto">
          {/* Sort Controls */}
          <div className="space-y-3">
            <h3 className="font-medium text-base">Sort By</h3>
            <Select
              value={localFilters.sort || 'recommended'}
              onValueChange={(value) => setLocalFilters({...localFilters, sort: value})}
            >
              <SelectTrigger className="w-full h-[44px] rounded-[20px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recommended">Recommended</SelectItem>
                <SelectItem value="price_asc">Price: Low to High</SelectItem>
                <SelectItem value="price_desc">Price: High to Low</SelectItem>
                <SelectItem value="cashback_desc">Cashback: High to Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Brand Filters with 44px touch targets */}
          <div className="space-y-3">
            <h3 className="font-medium text-base">Brands</h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {brands.map((brand) => (
                <div key={brand.id} className="flex items-center space-x-3 py-2">
                  <Checkbox
                    id={brand.id}
                    checked={localFilters.brands?.includes(brand.id) || false}
                    onCheckedChange={(checked) => {
                      const currentBrands = localFilters.brands || []
                      const newBrands = checked 
                        ? [...currentBrands, brand.id]
                        : currentBrands.filter(id => id !== brand.id)
                      setLocalFilters({...localFilters, brands: newBrands})
                    }}
                    className="h-5 w-5" // Ensure checkbox is touchable
                  />
                  <label 
                    htmlFor={brand.id} 
                    className="text-sm leading-none cursor-pointer flex-1 py-2" // 44px touch area
                  >
                    {brand.name}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          {/* Price Range with improved touch targets */}
          <div className="space-y-3">
            <h3 className="font-medium text-base">Price Range</h3>
            <div className="px-3">
              <Slider
                value={localFilters.priceRange || [0, 1000]}
                onValueChange={(value) => setLocalFilters({...localFilters, priceRange: value})}
                max={1000}
                step={10}
                className="w-full"
                // Custom styling for better touch targets
                style={{
                  '--thumb-size': '20px' // Larger thumb for touch
                }}
              />
              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>£{localFilters.priceRange?.[0] || 0}</span>
                <span>£{localFilters.priceRange?.[1] || 1000}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Action Buttons with 2025 Airbnb styling */}
        <div className="flex gap-3 pt-4 border-t">
          <TouchButton 
            variant="outline" 
            className="flex-1 h-[48px] rounded-[20px] border-gray-300" 
            onClick={onClose}
          >
            Cancel
          </TouchButton>
          <TouchButton 
            className="flex-1 h-[48px] rounded-[20px] bg-[#FF385C] hover:bg-[#E31C5F]" 
            onClick={handleApplyFilters}
          >
            Apply Filters
          </TouchButton>
        </div>
      </SheetContent>
    </Sheet>
  )
}

// Filter Button with count badge
const FilterButton = ({ onOpenFilter, activeFilters }: FilterButtonProps) => {
  const activeCount = Object.values(activeFilters).filter(Boolean).length
  
  return (
    <TouchButton
      variant="outline"
      className="relative rounded-[20px] border-[#FF385C] text-[#FF385C] h-[44px] px-4"
      onClick={onOpenFilter}
    >
      <SlidersHorizontal className="mr-2 h-5 w-5" />
      Filters
      {activeCount > 0 && (
        <Badge 
          className="absolute -top-2 -right-2 bg-[#FF385C] text-white min-w-[20px] h-[20px] rounded-full flex items-center justify-center text-xs"
        >
          {activeCount}
        </Badge>
      )}
    </TouchButton>
  )
}
```

**Files to Create/Modify:**
- `src/components/search/FilterControls.tsx` (major redesign with 80px clearance)
- `src/components/search/FilterDrawer.tsx` (new with 2025 patterns)
- `src/components/search/SortControls.tsx` (mobile optimization)
- `src/components/ui/FilterChips.tsx` (new with #FF385C styling)
- `src/hooks/useFilterState.ts` (new, integrating with usePagination)

---

## 📋 Implementation Checklist

### Pre-Development Phase
- [ ] Review existing component library (shadcn/ui) for compatibility
- [ ] Confirm 2025 Airbnb design token specifications (80px header, 50px search, 20px cards)
- [ ] Validate CLAUDE.md architectural patterns (usePagination, server-side data)
- [ ] Set up performance monitoring baselines
- [ ] Create comprehensive testing strategy

### Development Process Requirements
- [ ] **MANDATORY**: Use `usePagination` hooks for all paginated components
- [ ] **MANDATORY**: Use `createServerSupabaseReadOnlyClient()` for all data fetching
- [ ] **MANDATORY**: Maintain all Zod schema validation
- [ ] **MANDATORY**: Preserve DOMPurify sanitization
- [ ] **MANDATORY**: Keep rate limiting and CSP headers
- [ ] Follow atomic design principles (atoms → molecules → organisms)
- [ ] Implement progressive enhancement patterns
- [ ] Test on real devices (iOS Safari, Android Chrome)
- [ ] Maintain accessibility standards (WCAG 2.1 AA)
- [ ] Document component APIs thoroughly

### Quality Assurance Phase
- [ ] Cross-browser testing (Safari iOS, Chrome Android, Desktop browsers)
- [ ] Performance regression testing (Core Web Vitals maintenance)
- [ ] Accessibility audits (Screen readers, keyboard navigation)
- [ ] **INTEGRATION TESTING**: All components work with existing data functions
- [ ] **SECURITY TESTING**: All inputs properly validated and sanitized
- [ ] User acceptance testing with mobile-first scenarios

---

*Each user story integrates the latest 2025 Airbnb design patterns while maintaining 100% compatibility with CLAUDE.md architectural requirements. All implementations preserve existing functionality and security measures while delivering cutting-edge mobile UX.*