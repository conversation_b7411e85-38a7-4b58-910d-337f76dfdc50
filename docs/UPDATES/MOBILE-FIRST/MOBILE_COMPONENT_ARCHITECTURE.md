# 🏗️ Mobile Component Architecture

## Overview

This document outlines the technical architecture for mobile-first components in RebateRay, focusing on reusable, accessible, and performant component design patterns that extend the existing shadcn/ui foundation.

## 🎯 Architecture Principles

### 1. Mobile-First Design
- **Touch Targets**: Minimum 44px for all interactive elements
- **Progressive Enhancement**: Start with mobile, enhance for larger screens
- **Performance**: Optimized for slower mobile connections
- **Accessibility**: WCAG 2.1 AA compliance across all components

### 2. Component Composition
- **Atomic Design**: Build from atoms → molecules → organisms
- **shadcn/ui Foundation**: Extend existing components rather than replace
- **TypeScript-First**: Strict typing for all component interfaces
- **Testable**: Every component includes comprehensive tests

### 3. State Management
- **Local State**: Component-level state for UI interactions
- **URL State**: Pagination and filters via `usePagination` hooks
- **Server State**: React Query for data fetching
- **Persistent State**: localStorage for user preferences

## 📁 Component Organization

```
src/components/
├── ui/                     # Base UI components (shadcn/ui + extensions)
│   ├── touch/             # Touch-optimized components
│   │   ├── TouchButton.tsx
│   │   ├── TouchIcon.tsx
│   │   └── QuickActions.tsx
│   ├── mobile/            # Mobile-specific components
│   │   ├── BottomNavigation.tsx
│   │   ├── FilterDrawer.tsx
│   │   └── SearchChips.tsx
│   ├── layout/            # Layout and structure components
│   │   ├── ResponsiveGrid.tsx
│   │   ├── NavigationDrawer.tsx
│   │   └── EmptyState.tsx
│   └── enhanced/          # Enhanced shadcn components
│       ├── ProductModal.tsx
│       ├── FilterChips.tsx
│       └── SkeletonCard.tsx
├── layout/                # Layout components
├── pages/                 # Page-specific client components
├── search/                # Search-related components
└── debug/                 # Development tools
```

## 🧩 Core Component Specifications

### TouchButton Component

**Purpose**: Provides touch-optimized button interactions with haptic feedback simulation

**Interface:**
```typescript
interface TouchButtonProps extends ButtonProps {
  touchSize?: 'sm' | 'md' | 'lg' // 44px | 48px | 56px
  haptic?: boolean
  pressScale?: number
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  children: React.ReactNode
}
```

**Implementation Details:**
- Extends shadcn/ui Button component
- Uses Framer Motion for press animations
- Minimum touch target enforcement
- Haptic feedback simulation via Vibration API
- Keyboard navigation preserved

**Usage Example:**
```typescript
<TouchButton 
  touchSize="md" 
  haptic={true}
  variant="default"
  onClick={handleSave}
>
  Save Product
</TouchButton>
```

### ResponsiveGrid Component

**Purpose**: Mobile-first grid layouts with consistent breakpoints

**Interface:**
```typescript
interface ResponsiveGridProps {
  type: 'products' | 'brands' | 'retailers' | 'promotions'
  children: React.ReactNode
  className?: string
  gap?: 'sm' | 'md' | 'lg'
}
```

**Grid Patterns:**
```typescript
const gridClasses = {
  products: {
    base: "grid grid-cols-1 gap-4",
    sm: "sm:grid-cols-2 sm:gap-6", 
    lg: "lg:grid-cols-3",
    xl: "xl:grid-cols-4"
  },
  brands: {
    base: "grid grid-cols-3 gap-3",
    sm: "sm:grid-cols-4",
    md: "md:grid-cols-6",
    lg: "lg:gap-6"
  },
  retailers: {
    base: "grid grid-cols-2 gap-4",
    sm: "sm:grid-cols-3",
    md: "md:grid-cols-4"
  }
}
```

### FilterDrawer Component

**Purpose**: Mobile-optimized filter interface using slide-up drawer pattern

**Interface:**
```typescript
interface FilterDrawerProps {
  isOpen: boolean
  onClose: () => void
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  brands: Brand[]
  categories: Category[]
  priceRange: [number, number]
}
```

**Key Features:**
- Uses shadcn/ui Sheet for slide-up behavior
- Touch-optimized filter controls
- Real-time filter application
- Filter state persistence
- Clear visual feedback for active filters

### SearchBar Enhancement

**Purpose**: Always-visible search with mobile optimizations

**Interface:**
```typescript
interface SearchBarProps {
  initialValue?: string
  onSearch?: (query: string) => void
  onSearchSubmit?: () => void
  compact?: boolean // For mobile header
  showChips?: boolean
}
```

**Mobile Optimizations:**
- 48px minimum height for touch
- Always visible on mobile (removes hidden md:block)
- Quick search chips below input
- Enhanced autocomplete with shadcn/ui Command
- Voice search integration (progressive enhancement)

## 🎨 Design System Integration

### shadcn/ui Component Usage

**Current Components Used:**
- Button, Input, Card (basic usage)

**Proposed Enhanced Usage:**

#### Navigation & Layout
```typescript
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb"
```

#### Data Display
```typescript
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
```

#### Forms & Input
```typescript
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
```

#### Feedback
```typescript
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { Toast } from "@/components/ui/toast"
```

### Design Tokens

**Spacing (Mobile-First):**
```css
--spacing-touch-sm: 44px;  /* Minimum touch target */
--spacing-touch-md: 48px;  /* Comfortable touch target */
--spacing-touch-lg: 56px;  /* Large touch target */

--spacing-gap-mobile: 1rem;    /* 16px */
--spacing-gap-tablet: 1.5rem;  /* 24px */
--spacing-gap-desktop: 2rem;   /* 32px */
```

**Border Radius (Airbnb-inspired):**
```css
--radius-sm: 8px;   /* Small elements */
--radius-md: 12px;  /* Cards, buttons */
--radius-lg: 16px;  /* Larger containers */
--radius-xl: 20px;  /* Hero sections */
```

**Typography Scale:**
```css
--text-xs: 0.75rem;   /* 12px - Helper text */
--text-sm: 0.875rem;  /* 14px - Body text */
--text-base: 1rem;    /* 16px - Primary text */
--text-lg: 1.125rem;  /* 18px - Headings */
--text-xl: 1.25rem;   /* 20px - Large headings */
```

## 🔧 Component Implementation Patterns

### 1. Mobile-First Responsive Pattern

```typescript
const Component = () => {
  // Mobile-first styling
  const baseClasses = "flex flex-col p-4 rounded-xl"
  const responsiveClasses = "sm:flex-row sm:p-6 lg:p-8"
  
  return (
    <div className={cn(baseClasses, responsiveClasses)}>
      {/* Content */}
    </div>
  )
}
```

### 2. Touch Interaction Pattern

```typescript
const InteractiveComponent = ({ onAction }: Props) => {
  const [isPressed, setIsPressed] = useState(false)
  
  return (
    <motion.div
      whileTap={{ scale: 0.95 }}
      onTapStart={() => setIsPressed(true)}
      onTap={() => {
        triggerHaptic()
        onAction()
      }}
      className="min-h-[44px] min-w-[44px] cursor-pointer"
    >
      {/* Content */}
    </motion.div>
  )
}
```

### 3. Progressive Enhancement Pattern

```typescript
const EnhancedComponent = () => {
  const [supportsFeature, setSupportsFeature] = useState(false)
  
  useEffect(() => {
    setSupportsFeature('vibrate' in navigator)
  }, [])
  
  return (
    <div>
      <BasicComponent />
      {supportsFeature && <EnhancedFeature />}
    </div>
  )
}
```

### 4. Loading State Pattern

```typescript
const DataComponent = () => {
  const { data, isLoading, error } = useQuery('data', fetchData)
  
  if (isLoading) return <SkeletonComponent />
  if (error) return <ErrorState onRetry={refetch} />
  if (!data?.length) return <EmptyState />
  
  return <DataDisplay data={data} />
}
```

## 📱 Viewport-Specific Adaptations

### Mobile (320-768px)
- Single column layouts
- Bottom sheet navigation
- Large touch targets (44px+)
- Simplified information hierarchy

### Tablet (768-1024px)
- Two-column layouts
- Hybrid navigation (bottom + side)
- Medium touch targets (48px)
- Enhanced filter experiences

### Desktop (1024px+)
- Multi-column layouts
- Traditional navigation
- Hover interactions
- Power user features

## 🧪 Testing Strategy

### Component Testing
```typescript
// TouchButton.test.tsx
describe('TouchButton', () => {
  it('should have minimum 44px touch target', () => {
    render(<TouchButton>Test</TouchButton>)
    const button = screen.getByRole('button')
    
    expect(button).toHaveStyle({
      minHeight: '44px',
      minWidth: '44px'
    })
  })
  
  it('should trigger haptic feedback when enabled', async () => {
    const mockVibrate = jest.fn()
    Object.defineProperty(navigator, 'vibrate', {
      value: mockVibrate,
      configurable: true
    })
    
    render(<TouchButton haptic={true}>Test</TouchButton>)
    
    await user.click(screen.getByRole('button'))
    expect(mockVibrate).toHaveBeenCalled()
  })
})
```

### Integration Testing
```typescript
// Search functionality
describe('Mobile Search Integration', () => {
  it('should show search on mobile header', () => {
    render(<Header />, { viewport: 'mobile' })
    
    expect(screen.getByRole('searchbox')).toBeVisible()
    expect(screen.getByRole('searchbox')).not.toHaveClass('hidden')
  })
})
```

### Performance Testing
```typescript
// Component performance
describe('Component Performance', () => {
  it('should render large lists efficiently', () => {
    const startTime = performance.now()
    
    render(<ProductGrid products={largeProductList} />)
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(100) // 100ms limit
  })
})
```

## 🚀 Performance Considerations

### Bundle Size Optimization
- Tree-shakeable component exports
- Lazy loading for heavy components
- Code splitting by route

### Runtime Performance
- Memoized expensive calculations
- Virtual scrolling for large lists
- Optimistic UI updates

### Image Optimization
- WebP/AVIF format support
- Responsive image sizing
- Lazy loading with intersection observer

## 📊 Accessibility Standards

### WCAG 2.1 AA Compliance
- Minimum color contrast ratios
- Keyboard navigation support
- Screen reader compatibility
- Focus management

### Mobile Accessibility
- Touch target size compliance
- Gesture alternative inputs
- Orientation support
- Zoom compatibility (up to 200%)

## 🔄 Migration Strategy

### Phase 1: Foundation Components
1. TouchButton → Replace all Button instances
2. ResponsiveGrid → Update layout components
3. SearchBar → Fix mobile search accessibility

### Phase 2: Layout Components
1. BottomNavigation → Add to layout
2. FilterDrawer → Replace desktop filters
3. NavigationDrawer → Enhance mobile menu

### Phase 3: Enhanced Components
1. ProductCard → Progressive disclosure
2. Loading states → Add throughout app
3. Empty states → Improve UX

### Phase 4: Optimization
1. Performance tuning
2. Accessibility audits
3. User testing and feedback

---

*This architecture ensures scalable, maintainable, and performant mobile-first components that enhance the user experience across all devices while maintaining code quality and accessibility standards.*