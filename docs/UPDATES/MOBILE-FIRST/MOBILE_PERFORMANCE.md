# ⚡ Mobile Performance Optimization Strategy

## Overview

This document outlines comprehensive performance optimization strategies for RebateRay's mobile-first transformation, focusing on Core Web Vitals, mobile-specific optimizations, and real-world performance improvements.

## 🎯 Performance Targets

### Core Web Vitals (Mobile)
- **Largest Contentful Paint (LCP)**: <2.5 seconds
- **First Input Delay (FID)**: <100 milliseconds  
- **Cumulative Layout Shift (CLS)**: <0.1
- **First Contentful Paint (FCP)**: <1.5 seconds
- **Time to Interactive (TTI)**: <3.0 seconds on 3G

### Mobile-Specific Targets
- **Touch Response Time**: <16ms (60fps)
- **Scroll Performance**: Consistent 60fps scrolling
- **Bundle Size**: <500KB initial JavaScript
- **Image Loading**: Progressive with WebP/AVIF
- **Offline Support**: Core functionality available offline

## 🚀 Optimization Strategies

### 1. JavaScript Bundle Optimization

#### Code Splitting Strategy
```typescript
// Route-based code splitting
const HomePage = lazy(() => import('@/components/pages/HomePageClient'))
const ProductsPage = lazy(() => import('@/app/products/components/ProductsContent'))
const SearchPage = lazy(() => import('@/components/pages/SearchPageClient'))

// Component-based splitting for heavy components
const FilterDrawer = lazy(() => import('@/components/search/FilterDrawer'))
const ProductModal = lazy(() => import('@/components/ui/ProductModal'))
```

#### Bundle Analysis Configuration
```typescript
// next.config.js enhancement
const nextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          shadcn: {
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            name: 'shadcn-ui',
            chunks: 'all',
          },
          framer: {
            test: /[\\/]node_modules[\\/]framer-motion[\\/]/,
            name: 'framer-motion',
            chunks: 'all',
          }
        }
      }
    }
    return config
  },
  
  // Enable experimental features for performance
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'framer-motion']
  }
}
```

#### Tree Shaking Optimization
```typescript
// Optimized imports to enable tree shaking
// ❌ Avoid default imports for large libraries
import * as Icons from 'lucide-react'

// ✅ Use named imports instead  
import { Search, Heart, Share, ChevronDown } from 'lucide-react'

// ✅ Optimized shadcn/ui imports
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
```

### 2. Image Optimization Strategy

#### Next.js Image Configuration
```typescript
// next.config.js image optimization
const nextConfig = {
  images: {
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    domains: ['your-image-domain.com'],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 1 week
  }
}
```

#### Responsive Image Component
```typescript
// src/components/ui/OptimizedImage.tsx
interface OptimizedImageProps {
  src: string
  alt: string
  width: number
  height: number
  priority?: boolean
  className?: string
  sizes?: string
}

export const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  priority = false,
  className,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
}: OptimizedImageProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <Skeleton className="absolute inset-0 w-full h-full" />
      )}
      
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        sizes={sizes}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setHasError(true)
          setIsLoading(false)
        }}
      />
      
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <ImageIcon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
    </div>
  )
}
```

### 3. Virtual Scrolling Implementation

#### Virtual List Component
```typescript
// src/components/ui/VirtualList.tsx
interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  return (
    <div
      style={{ height: containerHeight }}
      className="overflow-auto"
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Usage in ProductGrid
const ProductGrid = ({ products }: { products: Product[] }) => {
  return (
    <VirtualList
      items={products}
      itemHeight={300}
      containerHeight={600}
      renderItem={(product) => <ProductCard product={product} />}
    />
  )
}
```

### 4. Service Worker Implementation

#### Service Worker Setup
```typescript
// public/sw.js
const CACHE_NAME = 'rebateray-v1'
const STATIC_CACHE = [
  '/',
  '/products',
  '/brands',
  '/manifest.json'
]

const API_CACHE_PATTERN = /^https:\/\/api\.rebateray\.com\/v1\//

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_CACHE))
  )
})

self.addEventListener('fetch', (event) => {
  if (event.request.method !== 'GET') return

  // Cache API responses
  if (API_CACHE_PATTERN.test(event.request.url)) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response) {
            // Serve from cache, update in background
            fetch(event.request).then((fetchResponse) => {
              cache.put(event.request, fetchResponse.clone())
            })
            return response
          }
          // Network first for API calls
          return fetch(event.request).then((fetchResponse) => {
            cache.put(event.request, fetchResponse.clone())
            return fetchResponse
          })
        })
      })
    )
  }
})
```

#### Service Worker Registration
```typescript
// src/lib/serviceWorker.ts
export const registerServiceWorker = () => {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration)
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError)
        })
    })
  }
}

// Register in app/layout.tsx
useEffect(() => {
  if (process.env.NODE_ENV === 'production') {
    registerServiceWorker()
  }
}, [])
```

### 5. Database Query Optimization

#### Optimized Data Fetching
```typescript
// src/lib/data/products.ts enhancement
export async function getProducts(
  supabase: SupabaseClient,
  filters: ProductFilters = {},
  options: {
    page?: number
    limit?: number
    fields?: string[]
  } = {}
) {
  const { page = 1, limit = 20, fields } = options
  
  let query = supabase
    .from('products')
    .select(fields?.join(',') || `
      id,
      name,
      slug,
      images,
      min_price,
      cashback_amount,
      brand:brands(id, name, logo_url),
      category:categories(id, name)
    `)
    .range((page - 1) * limit, page * limit - 1)
    .order('updated_at', { ascending: false })

  // Apply filters efficiently
  if (filters.brandId) {
    query = query.eq('brand_id', filters.brandId)
  }
  
  if (filters.categoryId) {
    query = query.eq('category_id', filters.categoryId)
  }
  
  if (filters.search) {
    query = query.textSearch('search_vector', filters.search)
  }

  const { data, error, count } = await query

  if (error) throw error

  return {
    products: data || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  }
}
```

#### Caching Strategy Enhancement
```typescript
// src/lib/utils/cache.ts
interface CacheOptions {
  ttl?: number
  staleWhileRevalidate?: number
  tags?: string[]
}

export function createCachedFunction<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  options: CacheOptions = {}
) {
  const { ttl = 5 * 60 * 1000, staleWhileRevalidate = 60 * 1000 } = options
  const cache = new Map<string, { data: TReturn; timestamp: number; stale: boolean }>()

  return async (...args: TArgs): Promise<TReturn> => {
    const key = JSON.stringify(args)
    const cached = cache.get(key)
    const now = Date.now()

    // Return fresh cache
    if (cached && now - cached.timestamp < ttl && !cached.stale) {
      return cached.data
    }

    // Return stale cache while revalidating
    if (cached && now - cached.timestamp < ttl + staleWhileRevalidate) {
      // Revalidate in background
      fn(...args).then((data) => {
        cache.set(key, { data, timestamp: now, stale: false })
      }).catch(() => {
        // Keep stale data on error
      })
      
      return cached.data
    }

    // Fetch fresh data
    try {
      const data = await fn(...args)
      cache.set(key, { data, timestamp: now, stale: false })
      return data
    } catch (error) {
      // Return stale data if available
      if (cached) {
        cached.stale = true
        return cached.data
      }
      throw error
    }
  }
}
```

### 6. Mobile-Specific Optimizations

#### Touch Performance
```typescript
// src/hooks/useOptimizedTouch.ts
export const useOptimizedTouch = () => {
  useEffect(() => {
    // Passive touch listeners for better scroll performance
    const options = { passive: true }
    
    const handleTouchStart = (e: TouchEvent) => {
      // Prevent 300ms click delay
      if (e.touches.length === 1) {
        e.preventDefault()
      }
    }

    document.addEventListener('touchstart', handleTouchStart, options)
    
    return () => {
      document.removeEventListener('touchstart', handleTouchStart)
    }
  }, [])
}
```

#### Intersection Observer for Lazy Loading
```typescript
// src/hooks/useIntersectionObserver.ts
export const useIntersectionObserver = (
  ref: RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    if (!ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(ref.current)

    return () => observer.disconnect()
  }, [ref, options])

  return isIntersecting
}

// Usage in ProductCard
const ProductCard = ({ product }: { product: Product }) => {
  const ref = useRef<HTMLDivElement>(null)
  const isVisible = useIntersectionObserver(ref)

  return (
    <div ref={ref}>
      {isVisible ? (
        <OptimizedImage src={product.image} alt={product.name} />
      ) : (
        <Skeleton className="aspect-[4/3]" />
      )}
    </div>
  )
}
```

### 7. Performance Monitoring

#### Web Vitals Tracking
```typescript
// src/components/performance/WebVitals.tsx enhancement
import { getCLS, getFCP, getFID, getLCP, getTTFB } from 'web-vitals'

interface WebVitalsProps {
  debug?: boolean
  reportToAnalytics?: boolean
}

export const WebVitals = ({ debug = false, reportToAnalytics = false }: WebVitalsProps) => {
  useEffect(() => {
    const handleMetric = (metric: any) => {
      if (debug) {
        console.log(metric)
      }
      
      if (reportToAnalytics) {
        // Send to analytics service
        gtag('event', metric.name, {
          event_category: 'Web Vitals',
          value: Math.round(metric.value),
          event_label: metric.id,
          non_interaction: true,
        })
      }
    }

    getCLS(handleMetric)
    getFCP(handleMetric)
    getFID(handleMetric)
    getLCP(handleMetric)
    getTTFB(handleMetric)
  }, [debug, reportToAnalytics])

  return null
}
```

#### Performance Budget Monitoring
```typescript
// src/lib/performance/budget.ts
export const performanceBudget = {
  // Bundle sizes (gzipped)
  javascript: 500 * 1024,      // 500KB
  css: 100 * 1024,             // 100KB
  images: 2 * 1024 * 1024,     // 2MB per page
  
  // Runtime metrics
  lcp: 2500,                   // 2.5s
  fid: 100,                    // 100ms
  cls: 0.1,                    // 0.1
  
  // Custom metrics
  timeToInteractive: 3000,     // 3s
  firstContentfulPaint: 1500,  // 1.5s
}

export const checkPerformanceBudget = async () => {
  const entries = performance.getEntriesByType('navigation')
  const navigation = entries[0] as PerformanceNavigationTiming
  
  const metrics = {
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
    firstByte: navigation.responseStart - navigation.requestStart,
  }
  
  // Check against budget and warn if exceeded
  Object.entries(metrics).forEach(([key, value]) => {
    const budget = performanceBudget[key as keyof typeof performanceBudget]
    if (typeof budget === 'number' && value > budget) {
      console.warn(`Performance budget exceeded for ${key}: ${value}ms > ${budget}ms`)
    }
  })
}
```

## 📊 Performance Testing Strategy

### Automated Performance Testing
```typescript
// tests/performance/lighthouse.test.ts
import { chromium } from 'playwright'

describe('Performance Tests', () => {
  test('should meet Core Web Vitals targets', async () => {
    const browser = await chromium.launch()
    const page = await browser.newPage()
    
    // Navigate to page
    await page.goto('http://localhost:3000')
    
    // Measure Core Web Vitals
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          resolve(entries.map(entry => ({
            name: entry.name,
            value: entry.value
          })))
        })
        
        observer.observe({ entryTypes: ['measure'] })
      })
    })
    
    await browser.close()
    
    // Assert performance targets
    expect(metrics.find(m => m.name === 'LCP')?.value).toBeLessThan(2500)
  })
})
```

### Real User Monitoring
```typescript
// src/lib/performance/rum.ts
export class RealUserMonitoring {
  private static instance: RealUserMonitoring
  private metrics: Map<string, number> = new Map()

  static getInstance() {
    if (!RealUserMonitoring.instance) {
      RealUserMonitoring.instance = new RealUserMonitoring()
    }
    return RealUserMonitoring.instance
  }

  trackMetric(name: string, value: number) {
    this.metrics.set(name, value)
    
    // Send to analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: value,
        user_agent: navigator.userAgent,
        connection_type: (navigator as any).connection?.effectiveType
      })
    }
  }

  trackUserTiming(name: string) {
    const startTime = performance.now()
    
    return () => {
      const duration = performance.now() - startTime
      this.trackMetric(name, duration)
    }
  }
}

// Usage in components
const ProductGrid = () => {
  const rum = RealUserMonitoring.getInstance()
  
  useEffect(() => {
    const endTiming = rum.trackUserTiming('product_grid_render')
    
    return () => {
      endTiming()
    }
  }, [])
  
  // Component content...
}
```

## 🎯 Success Metrics & Monitoring

### Performance KPIs
- **Mobile LCP**: <2.5s (target: <2.0s)
- **Mobile FID**: <100ms (target: <50ms)
- **Mobile CLS**: <0.1 (target: <0.05)
- **Bundle Size**: <500KB initial (target: <400KB)
- **Time to Interactive**: <3s on 3G (target: <2.5s)

### Monitoring Tools
- **Lighthouse CI**: Automated performance regression testing
- **Web Vitals**: Real-time Core Web Vitals monitoring
- **Bundle Analyzer**: JavaScript bundle size tracking
- **Performance Observer**: Custom metric collection

### Alerting Thresholds
```typescript
const performanceAlerts = {
  lcp: { warning: 2000, critical: 2500 },
  fid: { warning: 50, critical: 100 },
  cls: { warning: 0.05, critical: 0.1 },
  bundleSize: { warning: 400 * 1024, critical: 500 * 1024 }
}
```

---

*This performance optimization strategy ensures RebateRay delivers exceptional mobile performance while maintaining feature richness and user experience quality across all devices and network conditions.*