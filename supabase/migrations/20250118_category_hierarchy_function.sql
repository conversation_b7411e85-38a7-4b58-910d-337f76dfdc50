-- Create function to get category hierarchy (parent + all children)
CREATE OR R<PERSON>LACE FUNCTION get_category_hierarchy(category_slug TEXT)
RETURNS TABLE(id UUID, is_parent BOOLEAN) AS $$
WITH RECURSIVE hierarchy AS (
  -- Start with the requested category
  SELECT 
    c.id,
    c.slug,
    c.parent_id,
    0 as depth,
    CASE WHEN EXISTS(SELECT 1 FROM categories WHERE parent_id = c.id) THEN true ELSE false END as is_parent_category
  FROM categories c 
  WHERE c.slug = category_slug
  
  UNION ALL
  
  -- Recursively get all children (only if the root is a parent category)
  SELECT 
    c.id,
    c.slug,
    c.parent_id,
    h.depth + 1,
    false as is_parent_category
  FROM categories c
  JOIN hierarchy h ON c.parent_id = h.id
  WHERE h.is_parent_category = true  -- Only traverse if root is parent
)
SELECT 
  hierarchy.id,
  hierarchy.is_parent_category as is_parent
FROM hierarchy;
$$ LANGUAGE SQL STABLE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);