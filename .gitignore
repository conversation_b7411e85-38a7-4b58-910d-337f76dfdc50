# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.*

# Testing
/coverage

# Next.js
/.next/
/out/
.next/cache/
.next/out/

# Production
/build

# Environment variables
.env
.env.local
.env.*.local
.env.example
.env.development
.env.test
.env.production
.env.development.local
.env.test.local
.env.staging
.env.ci
src/env.mjs


# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
debug*.md

# Build output
.vercel/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Supabase
/supabase/backups/

# Documentation

docs/DEPLOYMENT_GUIDE.md

# ───────── Test artefacts ─────────
# Jest / Vitest / Mocha
coverage/
*.lcov

# Playwright / Cypress artefacts
test-results/
playwright-report/
cypress/videos/
cypress/screenshots/

# JUnit & other XML logs
*.xml
!tests/fixtures/*.xml   # (keep sample fixtures if you have any)

# Generic logs
*.log
*.tmp
test-results/.last-run.json
test-results/junit.xml


# Development
api_test.sh
api_test_output.txt
start-server.sh

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea/
.history/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development
.local/
.logs
*.log
docs/security_report.md
chanagelog_enhanced.txt
jest.setup.js
jest.setup.js
token.txt
jest.setup.js

# Sentry Config File
.env.sentry-build-plugin

playwright-report/index.html
src/__tests__/security/hmac.test.ts


# Snapshots & screenshots
**/__image_snapshots__/**/*
*.png
*.mp4
test-results/junit.xml
test-results/.last-run.json
